#===============================================================================
# @brief    Kconfig file.
# Copyright (c) @CompanyNameMagicTag 2023-2023. All rights reserved.
#===============================================================================
config SAMPLE_SUPPORT_I2C_MASTER
    bool
    prompt "Support I2C Master Sample."
    default n
    depends on SAMPLE_SUPPORT_I2C
    help
        This option means support I2C Master Sample.

config I2C_MASTER_SUPPORT_WRITEREAD
    bool
    prompt "I2C master support use writeread interface test."
    depends on SAMPLE_SUPPORT_I2C_MASTER
    default n

config I2C_MASTER_BUS_ID
    int
    prompt "Choose I2C master bus id."
    depends on SAMPLE_SUPPORT_I2C_MASTER
    default 1

config I2C_MASTER_SCL_PIN
    int
    prompt "Choose I2C master SCLK pin."
    depends on SAMPLE_SUPPORT_I2C_MASTER
    default 25

config I2C_MASTER_SDA_PIN
    int
    prompt "Choose I2C master SDA pin."
    depends on SAMPLE_SUPPORT_I2C_MASTER
    default 26

config I2C_MASTER_SCL_PIN_MODE
    int
    prompt "Choose I2C master SCLK pin mode."
    depends on SAMPLE_SUPPORT_I2C_MASTER
    default 28

config I2C_MASTER_SDA_PIN_MODE
    int
    prompt "Choose I2C master SDA pin mode."
    depends on SAMPLE_SUPPORT_I2C_MASTER
    default 29

config SAMPLE_SUPPORT_I2C_SLAVE
    bool
    prompt "Support I2C Slave Sample."
    default n
    depends on SAMPLE_SUPPORT_I2C
    help
        This option means support I2C Slave Sample.

config I2C_SLAVE_BUS_ID
    int
    prompt "Choose I2C slave bus id."
    depends on SAMPLE_SUPPORT_I2C_SLAVE
    default 1

config I2C_SLAVE_SCL_PIN
    int
    prompt "Choose I2C slave SCLK pin."
    depends on SAMPLE_SUPPORT_I2C_SLAVE
    default 25

config I2C_SLAVE_SDA_PIN
    int
    prompt "Choose I2C slave SDA pin."
    depends on SAMPLE_SUPPORT_I2C_SLAVE
    default 26

config I2C_SLAVE_SCL_PIN_MODE
    int
    prompt "Choose I2C slave SCLK pin mode."
    depends on SAMPLE_SUPPORT_I2C_SLAVE
    default 28

config I2C_SLAVE_SDA_PIN_MODE
    int
    prompt "Choose I2C slave SDA pin mode."
    depends on SAMPLE_SUPPORT_I2C_SLAVE
    default 29

config I2C_TRANSFER_LEN
    int
    prompt "Choose I2C transfer length."
    depends on SAMPLE_SUPPORT_I2C
    default 8