#===============================================================================
# @brief    Kconfig file.
# Copyright (c) @CompanyNameMagicTag 2023-2023. All rights reserved.
#===============================================================================

config USER_FIRMWARE_VERSION
    string
    prompt "User Defined Firmware Version"
    default "0.0.0.1"
    help
        This is a user defined firmware version string.
        The string must not exceed 40 characters.

config SYSTEM_CONTROL_ENABLE
    bool
    prompt "Enable system control."
    default n
    help
        This option means support system control config.

if SYSTEM_CONTROL_ENABLE
config POWER_SUPPLY_BY_LDO
    bool
    prompt "Power supply by ldo."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means Power supply by ldo.

config REDUCE_PERP_LS_FREQ
    bool
    prompt "Reduce the frequency of low-speed peripherals."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means reduce the frequency of low-speed peripherals.

config REDUCE_FREQ_DURING_WFI
    bool
    prompt "Reduce frequency during wfi."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means reduce frequency during wfi.

config ULTRA_DEEP_SLEEP_ENABLE
    bool
    prompt "Enable ultra-deep sleep."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means support ultra-deep sleep.

config PM_XO_FAST_START_ENABLE
    bool
    prompt "Enable xo fast start."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means support xo fast start.

config XO_32K_ENABLE
    bool
    prompt "Enable the xo 32k clock."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means enable the xo 32k clock.

config SUPPORT_CLOSE_ULP_WDT_DURING_SLP
    bool
    prompt "Support close ulp-wdt during sleep."
    default n
    depends on SYSTEM_CONTROL_ENABLE
    help
        This option means support close ulp-wdt during sleep.

endif

config SAMPLE_ENABLE
    bool
    prompt "Enable Sample."
    default n
    help
        This option means support Samples.

if SAMPLE_ENABLE
osource "application/samples/Kconfig"
endif

config SYSTEM_MOUSE_PIN_CONFIG
    bool
    prompt "Enable mouse pin config."
    default y
    help
        This option means support mouse pin config.

menu "System mouse Pin Configuration"
config MOUSE_PIN_LEFT
    int
    prompt "Mouse left key pinnum."
    default 0
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_MID
    int
    prompt "Mouse middle key pinnum."
    default 1
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_RIGHT
    int
    prompt "Mouse right key pinnum."
    default 2
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_QDEC_A
    int
    prompt "Mouse qdec a pinnum."
    default 3
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_QDEC_B
    int
    prompt "Mouse qdec b pinnum."
    default 4
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_QDEC_COMMON
    int
    prompt "Mouse qdec common pinnum."
    default 5
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_NRESET
    int
    prompt "Mouse sensor reset pinnum."
    default 21
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_MONTION
    int
    prompt "Mouse sensor montion pinnum."
    default 6
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_SPI_CLK
    int
    prompt "Mouse SPI clk pinnum."
    default 28
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_SPI_CS
    int
    prompt "Mouse SPI cs pinnum."
    default 27
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_SPI_MOSI
    int
    prompt "Mouse SPI MOSI pinnum."
    default 30
    depends on SYSTEM_MOUSE_PIN_CONFIG

config MOUSE_PIN_SPI_MISO
    int
    prompt "Mouse SPI MISO pinnum."
    default 31
    depends on SYSTEM_MOUSE_PIN_CONFIG
endmenu