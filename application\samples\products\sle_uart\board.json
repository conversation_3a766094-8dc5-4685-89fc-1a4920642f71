{"all": {"CLOCKSettings": {}, "SAMPLE Settings": {}, "PLIC Settings": {}, "MACRO Settings": {}, "gpio": {"GPIO": {}, "UART2": {"UART2_TXD": {"Pin Name": "S_AGPIO8"}, "UART2_RXD": {"Pin Name": "S_AGPIO9"}}, "UART3": {"UART3_TXD": {"Pin Name": "S_AGPIO12"}, "UART3_RXD": {"Pin Name": "S_AGPIO13"}}}, "pinName": ["S_AGPIO8", "S_AGPIO9", "S_AGPIO12", "S_AGPIO13"], "GPIO Settings": {}, "DMA Settings": {}}, "enabled": ["UART2", "UART3"], "info": {"board": "bs25", "chipName": "bs25", "clockName": "clock3", "seriesName": "bs25", "package": "QFN48", "package path": "", "package version": "1.0", "part": "", "needSDK": true, "pins": {}}, "part": {"UART2": {"UART2 Configuration": {"Send Mode": "POLLING", "Receive Mode": "INTERRUPT", "TX Interrrupt FIFO Level": "1_8", "RX Interrrupt FIFO Level": "1_8", "Baud Rate": 115200, "Word Length(Bit)": 8, "Parity": "None", "Stop Bits(Bit)": 1, "Hardware Flow Control": "Disable", "UART2_TXD": "S_AGPIO8", "UART2_RXD": "S_AGPIO9", "UART2_CTS": "PIN_NONE", "UART2_RTS": "PIN_NONE"}, "advancedSetting": {"PLIC Settings": {"IRQ_UART2": {"Name": "IRQ_UART2", "dependence": "", "Module": "UART2", "Priority": "1", "Enable": "disable", "key": "0"}}, "DMA Settings": {"UART2_RX": {"SrcPeriph": "UART2_RX", "dependence": "", "DestPeriph": "None", "Direction": "None", "key": "0", "Channel": "None", "Priority": "None", "SrcAddrInc": "UNALTERED", "DestAddrInc": "UNALTERED", "SrcBurst": "1", "DestBurst": "1", "SrcWidth": "BYTE", "DestWidth": "BYTE", "Enable": "disable", "Module": "UART2"}, "UART2_TX": {"SrcPeriph": "UART2_TX", "dependence": "", "DestPeriph": "None", "Direction": "None", "key": "0", "Channel": "None", "Priority": "None", "SrcAddrInc": "UNALTERED", "DestAddrInc": "UNALTERED", "SrcBurst": "1", "DestBurst": "1", "SrcWidth": "BYTE", "DestWidth": "BYTE", "Enable": "disable", "Module": "UART2"}}}, "pins": {"UART2_TXD": {"Pin Name": "S_AGPIO8", "dependence": true, "pin_mode": 1}, "UART2_RXD": {"Pin Name": "S_AGPIO9", "dependence": true, "pin_mode": 1}}}, "UART3": {"UART3 Configuration": {"Send Mode": "POLLING", "Receive Mode": "INTERRUPT", "TX Interrrupt FIFO Level": "1_8", "RX Interrrupt FIFO Level": "1_8", "Baud Rate": 115200, "Word Length(Bit)": 8, "Parity": "None", "Stop Bits(Bit)": 1, "Hardware Flow Control": "Disable", "UART3_TXD": "S_AGPIO12", "UART3_RXD": "S_AGPIO13", "UART3_CTS": "PIN_NONE", "UART3_RTS": "PIN_NONE"}, "advancedSetting": {"PLIC Settings": {"IRQ_UART3": {"Name": "IRQ_UART3", "dependence": "", "Module": "UART3", "Priority": "1", "Enable": "disable", "key": "0"}}, "DMA Settings": {"UART3_RX": {"SrcPeriph": "UART3_RX", "dependence": "", "DestPeriph": "None", "Direction": "None", "key": "0", "Channel": "None", "Priority": "None", "SrcAddrInc": "UNALTERED", "DestAddrInc": "UNALTERED", "SrcBurst": "1", "DestBurst": "1", "SrcWidth": "BYTE", "DestWidth": "BYTE", "Enable": "disable", "Module": "UART3"}, "UART3_TX": {"SrcPeriph": "UART3_TX", "dependence": "", "DestPeriph": "None", "Direction": "None", "key": "0", "Channel": "None", "Priority": "None", "SrcAddrInc": "UNALTERED", "DestAddrInc": "UNALTERED", "SrcBurst": "1", "DestBurst": "1", "SrcWidth": "BYTE", "DestWidth": "BYTE", "Enable": "disable", "Module": "UART3"}}}, "pins": {"UART3_TXD": {"Pin Name": "S_AGPIO12", "dependence": true, "pin_mode": 1}, "UART3_RXD": {"Pin Name": "S_AGPIO13", "dependence": true, "pin_mode": 1}}}}, "base": {"GPIO": {"GPIO": ""}, "DMA": {"SrcByteOrder": "Big endian", "DestByteOrder": "Big endian"}}, "selectPinName": {}, "configType": "menu", "deleteType": "moduleDelete", "globalCheckResult": {"UART2": {"status": "STATUS_OK", "tips": ""}, "UART3": {"status": "STATUS_OK", "tips": ""}}, "globalCheckItem": {"UART2": [{"check": "all:gpio:UART2:UART2_TXD", "tips": []}, {"check": "all:gpio:UART2:UART2_RXD", "tips": []}], "UART3": [{"check": "all:gpio:UART3:UART3_TXD", "tips": []}, {"check": "all:gpio:UART3:UART3_RXD", "tips": []}]}}