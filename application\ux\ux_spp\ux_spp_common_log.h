/*
 * Copyright (c) @CompanyNameMagicTag 2019-2020. All rights reserved.
 * File          ux_spp_common_log.h
 * Description:  Audio ux common spp log code define and log interface
 */

#ifndef __UX_SPP_COMMON_LOG_H__
#define __UX_SPP_COMMON_LOG_H__

#include "ha_common.h"

#define ux_spp_print_err(logNum, fmt, args...)  ha_base_print_err(0, fmt, ##args)
#define ux_spp_print_warn(logNum, fmt, args...) ha_base_print_warn(0, fmt, ##args)
#define ux_spp_print_info(logNum, fmt, args...) ha_base_print_info(0, fmt, ##args)
#define ux_spp_print_debug(logNum, fmt, args...) ha_base_print_debug(0, fmt, ##args)

/* log_num in ux spp module */
typedef enum {
    UX_SPP_LOG_NUM_0 = 0,
    UX_SPP_LOG_NUM_1,
    UX_SPP_LOG_NUM_2,
    UX_SPP_LOG_NUM_3,
    UX_SPP_LOG_NUM_4,
    UX_SPP_LOG_NUM_5,
    UX_SPP_LOG_NUM_6,
    UX_SPP_LOG_NUM_7,
    UX_SPP_LOG_NUM_8,
    UX_SPP_LOG_NUM_9,
    UX_SPP_LOG_NUM_10,
    UX_SPP_LOG_NUM_11,
    UX_SPP_LOG_NUM_12,
    UX_SPP_LOG_NUM_13 = 13,
    UX_SPP_LOG_NUM_100 = 100,
    UX_SPP_LOG_NUM_101,
    UX_SPP_LOG_NUM_102,
    UX_SPP_LOG_NUM_103,
    UX_SPP_LOG_NUM_104,
    UX_SPP_LOG_NUM_105,
    UX_SPP_LOG_NUM_106,
    UX_SPP_LOG_NUM_107,
    UX_SPP_LOG_NUM_108,
    UX_SPP_LOG_NUM_109,
    UX_SPP_LOG_NUM_110,
    UX_SPP_LOG_NUM_111,
    UX_SPP_LOG_NUM_112,
    UX_SPP_LOG_NUM_113,
    UX_SPP_LOG_NUM_114,
    UX_SPP_LOG_NUM_115,
    UX_SPP_LOG_NUM_116,
    UX_SPP_LOG_NUM_117,
    UX_SPP_LOG_NUM_118,
    UX_SPP_LOG_NUM_119,
    UX_SPP_LOG_NUM_120,
    UX_SPP_LOG_NUM_121,
    UX_SPP_LOG_NUM_122,
    UX_SPP_LOG_NUM_123,
    UX_SPP_LOG_NUM_124,
    UX_SPP_LOG_NUM_125,
    UX_SPP_LOG_NUM_126,
    UX_SPP_LOG_NUM_127,
    UX_SPP_LOG_NUM_128,
    UX_SPP_LOG_NUM_129,
    UX_SPP_LOG_NUM_130,
    UX_SPP_LOG_NUM_131,
    UX_SPP_LOG_NUM_132,
    UX_SPP_LOG_NUM_133,
    UX_SPP_LOG_NUM_134,
    UX_SPP_LOG_NUM_135,
    UX_SPP_LOG_NUM_136,
    UX_SPP_LOG_NUM_137,
    UX_SPP_LOG_NUM_138,
    UX_SPP_LOG_NUM_139,
    UX_SPP_LOG_NUM_140,
    UX_SPP_LOG_NUM_141,
    UX_SPP_LOG_NUM_142,
    UX_SPP_LOG_NUM_143,
    UX_SPP_LOG_NUM_144,
    UX_SPP_LOG_NUM_145,
    UX_SPP_LOG_NUM_146,
    UX_SPP_LOG_NUM_147,
    UX_SPP_LOG_NUM_148,
    UX_SPP_LOG_NUM_149,
    UX_SPP_LOG_NUM_150,
    UX_SPP_LOG_NUM_151,
    UX_SPP_LOG_NUM_152,
    UX_SPP_LOG_NUM_153,
    UX_SPP_LOG_NUM_154,
    UX_SPP_LOG_NUM_155,
    UX_SPP_LOG_NUM_156,
    UX_SPP_LOG_NUM_157,
    UX_SPP_LOG_NUM_158,
    UX_SPP_LOG_NUM_159,
    UX_SPP_LOG_NUM_160,
    UX_SPP_LOG_NUM_161,
    UX_SPP_LOG_NUM_162,
    UX_SPP_LOG_NUM_163,
    UX_SPP_LOG_NUM_164,
    UX_SPP_LOG_NUM_165,
    UX_SPP_LOG_NUM_166,
    UX_SPP_LOG_NUM_167,
    UX_SPP_LOG_NUM_168,
    UX_SPP_LOG_NUM_169,
    UX_SPP_LOG_NUM_170,
    UX_SPP_LOG_NUM_171,
    UX_SPP_LOG_NUM_172,
    UX_SPP_LOG_NUM_173,
    UX_SPP_LOG_NUM_174,
    UX_SPP_LOG_NUM_175,
    UX_SPP_LOG_NUM_176,
    UX_SPP_LOG_NUM_177,
    UX_SPP_LOG_NUM_178,
    UX_SPP_LOG_NUM_179,
    UX_SPP_LOG_NUM_180,
    UX_SPP_LOG_NUM_181,
    UX_SPP_LOG_NUM_182,
    UX_SPP_LOG_NUM_183,
    UX_SPP_LOG_NUM_184,
    UX_SPP_LOG_NUM_185,
    UX_SPP_LOG_NUM_186,
    UX_SPP_LOG_NUM_187,
    UX_SPP_LOG_NUM_188,
    UX_SPP_LOG_NUM_189,
    UX_SPP_LOG_NUM_190,
    UX_SPP_LOG_NUM_191,
    UX_SPP_LOG_NUM_192,
    UX_SPP_LOG_NUM_193,
    UX_SPP_LOG_NUM_194,
    UX_SPP_LOG_NUM_195,
    UX_SPP_LOG_NUM_196,
    UX_SPP_LOG_NUM_197,
    UX_SPP_LOG_NUM_198,
    UX_SPP_LOG_NUM_199,
    UX_SPP_LOG_NUM_200,
    UX_SPP_LOG_NUM_201,
    UX_SPP_LOG_NUM_202,
    UX_SPP_LOG_NUM_203,
    UX_SPP_LOG_NUM_204,
    UX_SPP_LOG_NUM_205,
    UX_SPP_LOG_NUM_206,
    UX_SPP_LOG_NUM_207,
    UX_SPP_LOG_NUM_208,
    UX_SPP_LOG_NUM_209,
    UX_SPP_LOG_NUM_210,
    UX_SPP_LOG_NUM_211,
    UX_SPP_LOG_NUM_212,
    UX_SPP_LOG_NUM_213,
    UX_SPP_LOG_NUM_214,
    UX_SPP_LOG_NUM_215,
    UX_SPP_LOG_NUM_216,
    UX_SPP_LOG_NUM_217,
    UX_SPP_LOG_NUM_218 = 218,
    UX_SPP_LOG_NUM_219 = 219,
    UX_SPP_LOG_NUM_220,
    UX_SPP_LOG_NUM_221,
    UX_SPP_LOG_NUM_222,
    UX_SPP_LOG_NUM_223,
    UX_SPP_LOG_NUM_224,
    UX_SPP_LOG_NUM_225,
    UX_SPP_LOG_NUM_226 = 226,
    UX_SPP_LOG_NUM_227,
    UX_SPP_LOG_NUM_228,
    UX_SPP_LOG_NUM_229,
    UX_SPP_LOG_NUM_230,
    UX_SPP_LOG_NUM_231,
    UX_SPP_LOG_NUM_232,
    UX_SPP_LOG_NUM_233,
    UX_SPP_LOG_NUM_234,
    UX_SPP_LOG_NUM_235,
    UX_SPP_LOG_NUM_236,
    UX_SPP_LOG_NUM_237,
    UX_SPP_LOG_NUM_238,
    UX_SPP_LOG_NUM_239,
    UX_SPP_LOG_NUM_240,
    UX_SPP_LOG_NUM_241,
    UX_SPP_LOG_NUM_242,
    UX_SPP_LOG_NUM_243,
    UX_SPP_LOG_NUM_244,
    UX_SPP_LOG_NUM_245,
    UX_SPP_LOG_NUM_246,
    UX_SPP_LOG_NUM_247,
    UX_SPP_LOG_NUM_248,
    UX_SPP_LOG_NUM_249,
    UX_SPP_LOG_NUM_250,
    UX_SPP_LOG_NUM_251,
    UX_SPP_LOG_NUM_252,
    UX_SPP_LOG_NUM_253,
    UX_SPP_LOG_NUM_254,
    UX_SPP_LOG_NUM_255,
    UX_SPP_LOG_NUM_256,
    UX_SPP_LOG_NUM_257,
    UX_SPP_LOG_NUM_258,
    UX_SPP_LOG_NUM_259,
    UX_SPP_LOG_NUM_260,
    UX_SPP_LOG_NUM_261,
    UX_SPP_LOG_NUM_262,
    UX_SPP_LOG_NUM_263,
    UX_SPP_LOG_NUM_264,
    UX_SPP_LOG_NUM_265,
    UX_SPP_LOG_NUM_266,
    UX_SPP_LOG_NUM_267,
    UX_SPP_LOG_NUM_268,
    UX_SPP_LOG_NUM_269,
    UX_SPP_LOG_NUM_270,
    UX_SPP_LOG_NUM_271,
    UX_SPP_LOG_NUM_272,
    UX_SPP_LOG_NUM_273,
    UX_SPP_LOG_NUM_274,
    UX_SPP_LOG_NUM_275,
    UX_SPP_LOG_NUM_276,
    UX_SPP_LOG_NUM_277,
    UX_SPP_LOG_NUM_278,
    UX_SPP_LOG_NUM_279,
    UX_SPP_LOG_NUM_280,
    UX_SPP_LOG_NUM_281,
    UX_SPP_LOG_NUM_282,
    UX_SPP_LOG_NUM_283,
    UX_SPP_LOG_NUM_284,
    UX_SPP_LOG_NUM_285,
    UX_SPP_LOG_NUM_286,
    UX_SPP_LOG_NUM_287,
    UX_SPP_LOG_NUM_288,
    UX_SPP_LOG_NUM_289,
    UX_SPP_LOG_NUM_290,
    UX_SPP_LOG_NUM_291,
    UX_SPP_LOG_NUM_292,
    UX_SPP_LOG_NUM_293,
    UX_SPP_LOG_NUM_294,
    UX_SPP_LOG_NUM_295,
    UX_SPP_LOG_NUM_296,
    UX_SPP_LOG_NUM_297,
    UX_SPP_LOG_NUM_298,
    UX_SPP_LOG_NUM_299,
    UX_SPP_LOG_NUM_300,
    UX_SPP_LOG_NUM_301,
    UX_SPP_LOG_NUM_302,
    UX_SPP_LOG_NUM_303,
    UX_SPP_LOG_NUM_304,
    UX_SPP_LOG_NUM_305,
    UX_SPP_LOG_NUM_306,
    UX_SPP_LOG_NUM_307,
    UX_SPP_LOG_NUM_308,
    UX_SPP_LOG_NUM_309,
    UX_SPP_LOG_NUM_310,
    UX_SPP_LOG_NUM_311,
    UX_SPP_LOG_NUM_312,
    UX_SPP_LOG_NUM_313,
    UX_SPP_LOG_NUM_314,
    UX_SPP_LOG_NUM_315,
    UX_SPP_LOG_NUM_316,
    UX_SPP_LOG_NUM_317,
    UX_SPP_LOG_NUM_318,
    UX_SPP_LOG_NUM_319,
    UX_SPP_LOG_NUM_320,
    UX_SPP_LOG_NUM_321,
    UX_SPP_LOG_NUM_322,
    UX_SPP_LOG_NUM_323,
    UX_SPP_LOG_NUM_324,
    UX_SPP_LOG_NUM_325,
    UX_SPP_LOG_NUM_326,
    UX_SPP_LOG_NUM_327,
    UX_SPP_LOG_NUM_328,
    UX_SPP_LOG_NUM_329,
    UX_SPP_LOG_NUM_330,
    UX_SPP_LOG_NUM_331,
    UX_SPP_LOG_NUM_332,
    UX_SPP_LOG_NUM_333,
    UX_SPP_LOG_NUM_334,
    UX_SPP_LOG_NUM_335,
    UX_SPP_LOG_NUM_336,
    UX_SPP_LOG_NUM_337,
    UX_SPP_LOG_NUM_338,
    UX_SPP_LOG_NUM_339,
    UX_SPP_LOG_NUM_340,
    UX_SPP_LOG_NUM_341,
    UX_SPP_LOG_NUM_342,
    UX_SPP_LOG_NUM_343,
    UX_SPP_LOG_NUM_344,
    UX_SPP_LOG_NUM_345,
    UX_SPP_LOG_NUM_346,
    UX_SPP_LOG_NUM_347,
    UX_SPP_LOG_NUM_348,
    UX_SPP_LOG_NUM_349,
    UX_SPP_LOG_NUM_350,
    UX_SPP_LOG_NUM_351,
    UX_SPP_LOG_NUM_352,
    UX_SPP_LOG_NUM_353,
    UX_SPP_LOG_NUM_354,
    UX_SPP_LOG_NUM_355,
    UX_SPP_LOG_NUM_356,
    UX_SPP_LOG_NUM_357,
    UX_SPP_LOG_NUM_358,
    UX_SPP_LOG_NUM_359,
    UX_SPP_LOG_NUM_360,
    UX_SPP_LOG_NUM_361,
    UX_SPP_LOG_NUM_362,
    UX_SPP_LOG_NUM_363,
    UX_SPP_LOG_NUM_364,
    UX_SPP_LOG_NUM_365,
    UX_SPP_LOG_NUM_366,
    UX_SPP_LOG_NUM_367,
    UX_SPP_LOG_NUM_368,
    UX_SPP_LOG_NUM_369,
    UX_SPP_LOG_NUM_370,
    UX_SPP_LOG_NUM_371,
    UX_SPP_LOG_NUM_372,
    UX_SPP_LOG_NUM_373,
    UX_SPP_LOG_NUM_374,
    UX_SPP_LOG_NUM_375,
    UX_SPP_LOG_NUM_376,
    UX_SPP_LOG_NUM_377,
    UX_SPP_LOG_NUM_378,
    UX_SPP_LOG_NUM_379,
    UX_SPP_LOG_NUM_380,
    UX_SPP_LOG_NUM_381,
    UX_SPP_LOG_NUM_382,
    UX_SPP_LOG_NUM_383,
    UX_SPP_LOG_NUM_384,
    UX_SPP_LOG_NUM_385,
    UX_SPP_LOG_NUM_386,
    UX_SPP_LOG_NUM_387,
    UX_SPP_LOG_NUM_388,
    UX_SPP_LOG_NUM_389,
    UX_SPP_LOG_NUM_390,
    UX_SPP_LOG_NUM_391,
    UX_SPP_LOG_NUM_392,
    UX_SPP_LOG_NUM_393,
    UX_SPP_LOG_NUM_394,
    UX_SPP_LOG_NUM_395,
    UX_SPP_LOG_NUM_396,
    UX_SPP_LOG_NUM_397,
    UX_SPP_LOG_NUM_398,
    UX_SPP_LOG_NUM_399,
    UX_SPP_LOG_NUM_400,
    UX_SPP_LOG_NUM_401,
    UX_SPP_LOG_NUM_402,
    UX_SPP_LOG_NUM_403,
    UX_SPP_LOG_NUM_404,
    UX_SPP_LOG_NUM_405,
    UX_SPP_LOG_NUM_406,
    UX_SPP_LOG_NUM_407,
    UX_SPP_LOG_NUM_408,
    UX_SPP_LOG_NUM_409,
    UX_SPP_LOG_NUM_410,
    UX_SPP_LOG_NUM_411,
    UX_SPP_LOG_NUM_412,
    UX_SPP_LOG_NUM_413,
    UX_SPP_LOG_NUM_414,
    UX_SPP_LOG_NUM_415,
    UX_SPP_LOG_NUM_416,
    UX_SPP_LOG_NUM_417,
    UX_SPP_LOG_NUM_418,
    UX_SPP_LOG_NUM_419,
    UX_SPP_LOG_NUM_420,
    UX_SPP_LOG_NUM_421,
    UX_SPP_LOG_NUM_422,
    UX_SPP_LOG_NUM_423,
    UX_SPP_LOG_NUM_424,
    UX_SPP_LOG_NUM_425,
    UX_SPP_LOG_NUM_426,
    UX_SPP_LOG_NUM_427,
    UX_SPP_LOG_NUM_428,
    UX_SPP_LOG_NUM_429,
    UX_SPP_LOG_NUM_430,
    UX_SPP_LOG_NUM_431,
    UX_SPP_LOG_NUM_432,
    UX_SPP_LOG_NUM_433,
    UX_SPP_LOG_NUM_434,
    UX_SPP_LOG_NUM_435,
    UX_SPP_LOG_NUM_436,
    UX_SPP_LOG_NUM_437,
    UX_SPP_LOG_NUM_438,
    UX_SPP_LOG_NUM_439,
    UX_SPP_LOG_NUM_440,
    UX_SPP_LOG_NUM_441,
    UX_SPP_LOG_NUM_442,
    UX_SPP_LOG_NUM_443,
    UX_SPP_LOG_NUM_444,
    UX_SPP_LOG_NUM_445,
    UX_SPP_LOG_NUM_446,
    UX_SPP_LOG_NUM_447,
    UX_SPP_LOG_NUM_448,
    UX_SPP_LOG_NUM_449,
    UX_SPP_LOG_NUM_450,
    UX_SPP_LOG_NUM_451,
    UX_SPP_LOG_NUM_452,
    UX_SPP_LOG_NUM_453,
    UX_SPP_LOG_NUM_454,
    UX_SPP_LOG_NUM_455,
    UX_SPP_LOG_NUM_456,
    UX_SPP_LOG_NUM_457,
    UX_SPP_LOG_NUM_458,
    UX_SPP_LOG_NUM_459,
    UX_SPP_LOG_NUM_460,
    UX_SPP_LOG_NUM_461,
    UX_SPP_LOG_NUM_462,
    UX_SPP_LOG_NUM_463,
    UX_SPP_LOG_NUM_464,
    UX_SPP_LOG_NUM_465,
    UX_SPP_LOG_NUM_466,
    UX_SPP_LOG_NUM_467,
    UX_SPP_LOG_NUM_468,
    UX_SPP_LOG_NUM_469,
    UX_SPP_LOG_NUM_470,
    UX_SPP_LOG_NUM_471,
    UX_SPP_LOG_NUM_472,
    UX_SPP_LOG_NUM_473,
    UX_SPP_LOG_NUM_474,
    UX_SPP_LOG_NUM_475,
    UX_SPP_LOG_NUM_476,
    UX_SPP_LOG_NUM_477,
    UX_SPP_LOG_NUM_478,
    UX_SPP_LOG_NUM_479,
    UX_SPP_LOG_NUM_480,
    UX_SPP_LOG_NUM_481,
    UX_SPP_LOG_NUM_482,
    UX_SPP_LOG_NUM_483,
    UX_SPP_LOG_NUM_484,
    UX_SPP_LOG_NUM_485,
    UX_SPP_LOG_NUM_486,
    UX_SPP_LOG_NUM_487,
    UX_SPP_LOG_NUM_488,
    UX_SPP_LOG_NUM_489,
    UX_SPP_LOG_NUM_490,
    UX_SPP_LOG_NUM_491,
    UX_SPP_LOG_NUM_492,
    UX_SPP_LOG_NUM_493,
    UX_SPP_LOG_NUM_494,
    UX_SPP_LOG_NUM_495,
    UX_SPP_LOG_NUM_496,
    UX_SPP_LOG_NUM_497,
    UX_SPP_LOG_NUM_498,
    UX_SPP_LOG_NUM_499,
    UX_SPP_LOG_NUM_500,
    UX_SPP_LOG_NUM_501,
    UX_SPP_LOG_NUM_502,
    UX_SPP_LOG_NUM_503,
    UX_SPP_LOG_NUM_504,
    UX_SPP_LOG_NUM_505,
    UX_SPP_LOG_NUM_506,
    UX_SPP_LOG_NUM_507,
    UX_SPP_LOG_NUM_508,
    UX_SPP_LOG_NUM_509,
    UX_SPP_LOG_NUM_510,
    UX_SPP_LOG_NUM_511,
    UX_SPP_LOG_NUM_512,
    UX_SPP_LOG_NUM_513,
    UX_SPP_LOG_NUM_514,
    UX_SPP_LOG_NUM_515,
    UX_SPP_LOG_NUM_516,
    UX_SPP_LOG_NUM_517,
    UX_SPP_LOG_NUM_518,
    UX_SPP_LOG_NUM_519,
    UX_SPP_LOG_NUM_520,
    UX_SPP_LOG_NUM_521,
    UX_SPP_LOG_NUM_522,
    UX_SPP_LOG_NUM_523,
    UX_SPP_LOG_NUM_524,
    UX_SPP_LOG_NUM_525,
    UX_SPP_LOG_NUM_526,
    UX_SPP_LOG_NUM_527,
    UX_SPP_LOG_NUM_528,
    UX_SPP_LOG_NUM_529,
    UX_SPP_LOG_NUM_530,
    UX_SPP_LOG_NUM_531,
    UX_SPP_LOG_NUM_532,
    UX_SPP_LOG_NUM_533,
    UX_SPP_LOG_NUM_534,
    UX_SPP_LOG_NUM_535,
    UX_SPP_LOG_NUM_536,
    UX_SPP_LOG_NUM_537,
    UX_SPP_LOG_NUM_538,
    UX_SPP_LOG_NUM_539,
    UX_SPP_LOG_NUM_540,
    UX_SPP_LOG_NUM_541,
    UX_SPP_LOG_NUM_542,
    UX_SPP_LOG_NUM_543,
    UX_SPP_LOG_NUM_544,
    UX_SPP_LOG_NUM_545,
    UX_SPP_LOG_NUM_546,
    UX_SPP_LOG_NUM_547,
    UX_SPP_LOG_NUM_548,
    UX_SPP_LOG_NUM_549,
    UX_SPP_LOG_NUM_550,
    UX_SPP_LOG_NUM_551,
    UX_SPP_LOG_NUM_552,
    UX_SPP_LOG_NUM_553,
    UX_SPP_LOG_NUM_554,
    UX_SPP_LOG_NUM_555,
    UX_SPP_LOG_NUM_556,
    UX_SPP_LOG_NUM_557,
    UX_SPP_LOG_NUM_558,
    UX_SPP_LOG_NUM_559,
    UX_SPP_LOG_NUM_560,
    UX_SPP_LOG_NUM_561,
    UX_SPP_LOG_NUM_562,
    UX_SPP_LOG_NUM_563,
    UX_SPP_LOG_NUM_564,
    UX_SPP_LOG_NUM_565,
    UX_SPP_LOG_NUM_566,
    UX_SPP_LOG_NUM_567,
    UX_SPP_LOG_NUM_568,
    UX_SPP_LOG_NUM_569,
    UX_SPP_LOG_NUM_570,
    UX_SPP_LOG_NUM_571,
    UX_SPP_LOG_NUM_572,
} ux_spp_log_num_t;

#endif
