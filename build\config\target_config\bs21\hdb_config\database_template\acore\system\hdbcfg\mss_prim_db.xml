﻿<?xml version="1.0" encoding="utf-8" ?>
<MSS>
    <SUBSYSTEM NAME="bt_core" DATA_STRUCT_FILE="..\diag\prot_core_hso_msg_struct_def.txt" DESCRIPTION="" MULTIMODE="bt_core">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="bt_status" DATA_STRUCT_FILE="..\diag\bt_status_hso_msg_struct_def.txt" DESCRIPTION="" MULTIMODE="bt_status">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="acore" DATA_STRUCT_FILE="..\diag\prot_core_hso_msg_struct_def.txt" DESCRIPTION="" MULTIMODE="acore">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="dsp_core" DATA_STRUCT_FILE="..\diag\prot_core_hso_msg_struct_def.txt" DESCRIPTION="" MULTIMODE="dsp_core">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="apps_core" DATA_STRUCT_FILE="..\diag\apps_core_hso_msg_struct_def.txt" DESCRIPTION="" MULTIMODE="apps_core">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="prot_core" DATA_STRUCT_FILE="..\diag\prot_core_hso_msg_struct_def.txt" DESCRIPTION="" MULTIMODE="prot_core">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="ota_msg" DATA_STRUCT_FILE="..\diag\fix_struct_def.txt" DESCRIPTION="" MULTIMODE="ota_msg">
        <MSG_LOG>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
    <SUBSYSTEM NAME="fix_msg" DATA_STRUCT_FILE="..\diag\fix_struct_def.txt" DESCRIPTION="" MULTIMODE="fix_msg">
        <MSG_LOG>
            <!-- OM_MSG_TYPE_LAST = 16-->
            <MSG NAME="EXCEPTION_LAST_RUN_INFO" ID="0x100003"/>
        </MSG_LOG>
        <LAYER_LOG>
        </LAYER_LOG>
        <USERPLANE_LOG>
        </USERPLANE_LOG>
    </SUBSYSTEM>
</MSS>
