#===============================================================================
# @brief    cmake file
# Copyright (c) @CompanyNameMagicTag 2022-2022. All rights reserved.
#===============================================================================
add_subdirectory_if_exist(standard)
add_subdirectory_if_exist(tag)
add_subdirectory_if_exist(ate)
add_subdirectory_if_exist(mouse)
add_subdirectory_if_exist(sle_dongle)
add_subdirectory_if_exist(ble_mouse)
add_subdirectory_if_exist(slekey)
add_subdirectory_if_exist(carkey)
add_subdirectory_if_exist(standard_mini)
