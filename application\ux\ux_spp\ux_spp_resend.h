/*
 * Copyright (c) @CompanyNameMagicTag 2020-2020. All rights reserved.
 * File          ux_spp_resend.h
 * Description:  Audio ux spp resend msg due to handover
 */
#ifndef __UX_SPP_RESEND_H__
#define __UX_SPP_RESEND_H__

#include <los_typedef.h>

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif

typedef enum {
    UX_SPP_RESEND_GET_DEVICE_VERSION,
    UX_SPP_RESEND_GET_BATTERY_PERCENTAGE,
    UX_SPP_RESEND_GET_DOUBLE_CLICK,
    UX_SPP_RESEND_GET_ANC_SWITCH,
    UX_SPP_RESEND_GET_ANC_MODE_AND_INDEX,
    UX_SPP_RESEND_GET_PAIR_FREE_DEV_INFO,
    UX_SPP_RESEND_GET_FONT_TYPE,
    UX_SPP_RESEND_GET_TOUCH_PAD_SWITCH,
    UX_SPP_RESEND_GET_NOISY_CONTROL_FUNC,
    UX_SPP_RESEND_GET_LONG_PRESS_FUNC,
    UX_SPP_RESEND_GET_WEAR_STATE,
    UX_SPP_RESEND_SET_DOUBLE_CLICK_TO_TWS,
    UX_SPP_RESEND_SET_DOUBLE_CLICK,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS,
    UX_SPP_RESEND_SET_ANC_SWITCH,
    UX_SPP_RESEND_SET_ANC_INDEX_TO_TWS,
    UX_SPP_RESEND_SET_ANC_INDEX,
    UX_SPP_RESEND_SET_FONT_TYPE_TO_TWS,
    UX_SPP_RESEND_SET_FONT_TYPE,
    UX_SPP_RESEND_SET_HD_SCO_SWITCH_ACK_PHONE,
    UX_SPP_RESEND_GET_HD_SCO_SWITCH,
    UX_SPP_RESEND_SET_TOUCH_PAD_SWITCH_TO_TWS,
    UX_SPP_RESEND_SET_TOUCH_PAD_SWITCH,
    UX_SPP_RESEND_SET_NOISY_CONTROL_FUNC_TO_TWS,
    UX_SPP_RESEND_SET_NOISY_CONTROL_FUNC,
    UX_SPP_RESEND_SET_ANC_MODE,
    UX_SPP_RESEND_SET_LONG_PRESS_FUNC,
    UX_SPP_RESEND_SET_LONG_PRESS_FUNC_TO_TWS,
    UX_SPP_RESEND_UPLOAD_ASSEMBLE_MUSIC_TO_PHONE,
    UX_SPP_RESEND_SET_EAR_PLUGS_DETECT_TO_TWS,
    UX_SPP_RESEND_SET_EAR_PLUGS_DETECT_ACK_PHONE,
    UX_SPP_RESEND_UPLOAD_EAR_PLUGS_RESULT_TO_PHONE,
    UX_SPP_RESEND_SET_WEAR_DETECTION_ACK_PHONE,
    UX_SPP_RESEND_GET_CS_RESULT,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS_EFFECT,
    UX_SPP_RESEND_GET_MULTIPLE_DEVICE_ABILITY,
    UX_SPP_RESEND_SET_MULTIPLE_DEVICE_SWITCH,
    UX_SPP_RESEND_GET_MULTIPLE_DEVICE_SWITCH,
    UX_SPP_RESEND_SET_RECORDING_MODE_ACK_PHONE,
    UX_SPP_RESEND_GET_RECORDING_MODE,
    UX_SPP_RESEND_GET_WEAR_DETECTION_SWITCH,
    UX_SPP_RESEND_GET_DORA_ABLITY,
    UX_SPP_RESEND_SET_USER_EQ_SETTING_ACK_PHONE,
    UX_SPP_RESEND_GET_USER_EQ_SETTING,
    UX_SPP_RESEND_BUSINESS_TYPE_MAX,
} ux_spp_resend_business_type_t;

typedef enum {
    UX_SPP_RESEND_DOUBLE_CLICK_TYPE,
    UX_SPP_RESEND_DOUBLE_CLICK_VALUE,
    UX_SPP_RESEND_ANC_SWITCH_VALUE,
    UX_SPP_RESEND_ANC_INDEX_VALUE,
    UX_SPP_RESEND_FONT_VALUE,
    UX_SPP_RESEND_ANC_SWITCH_TYPE,
    UX_SPP_RESEND_ANC_INDEX_TYPE,
    UX_SPP_RESEND_DOUBLE_CLICK_SOURCE_TYPE,
    UX_SPP_RESEND_DOUBLE_CLICK_VALUE_LEFT,
    UX_SPP_RESEND_DOUBLE_CLICK_VALUE_RIGHT,
    UX_SPP_RESEND_TOUCH_PAD_SWITCH_VALUE,
    UX_SPP_RESEND_SET_NOISY_CONTROL_TO_TWS_VALUE,
    UX_SPP_RESEND_SET_NOISY_CONTROL_VALUE,
    UX_SPP_RESEND_SET_LONG_PRESS_TYPE,
    UX_SPP_RESEND_LONG_PRESS_TO_TWS_TYPE,
    UX_SPP_RESEND_LONG_PRESS_TO_TWS_VALUE,
    UX_SPP_RESEND_LONG_PRESS_TO_TWS_SOURCE,
    UX_SPP_RESEND_GET_FREE_PAIR_DEV_INFO_VALUE,
    UX_SPP_RESEND_ASSEMBLE_MUSIC_VALUE,
    UX_SPP_RESEND_AI_OR_AH_SWITCH_VALUE,
    UX_SPP_RESEND_EAR_PLUGS_RESULT_VALUE,
    UX_SPP_RESEND_ANC_EFFECT_VALUE_1,
    UX_SPP_RESEND_ANC_EFFECT_VALUE_2,
    UX_SPP_RESEND_ANC_EFFECT_VALUE_3,
    UX_SPP_RESEND_ANC_EFFECT_VALUE_4,
    UX_SPP_RESEND_ANC_EFFECT_VALUE_5,
    UX_SPP_RESEND_SET_MULTIPLE_DEVICE_SWITCH_VALUE,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS_EFFECT_ANC_SWITCH,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS_EFFECT_TONE_STATE,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS_EFFECT_INTELLIGENT_SWITCH,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS_EFFECT_ANC_PARAM,
    UX_SPP_RESEND_SET_ANC_SWITCH_TO_TWS_EFFECT_AH_SWITCH,
    UX_SPP_RESEND_VALUE_MAX,
} ux_spp_resend_value_t;

uint32_t ux_spp_set_resend_type_other(const UINT8* resend_type, uint32_t length);
uint32_t ux_spp_set_resend_value_other(const UINT8* resend_value, uint32_t length);
uint8_t ux_spp_get_resend_value_other(ux_spp_resend_value_t value);

VOID ux_spp_set_resend_flag(uint8_t tmp);
uint8_t ux_spp_get_resend_flag(void);

void ux_spp_set_resend_type(uint8_t type, uint8_t need_resend_flag);
void ux_spp_set_resend_value(uint8_t value_type, uint8_t value);

void ux_spp_master_resend_msg(void);
void ux_spp_resend_msg_to_master(void);

void ux_spp_send_data_to_tws_resend(uint8_t command_id, uint8_t resend_flag);
void ux_spp_master_handle_resend_msg_self_effect(void);

#ifdef CONFIG_AUDIO_EFFECT_CSTM_EQ
VOID ux_spp_event_set_user_eq_setting_ack_phone(VOID);
VOID ux_spp_event_get_user_eq_setting(uint8_t *p_data, uint16_t data_len);
#endif

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif
#endif
