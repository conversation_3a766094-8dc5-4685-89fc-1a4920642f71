name: Build and Compile

on:
  push:
    branches:
      - master
      - main
      - dev
      - feature*
      - 'feature/**'

  pull_request:
    branches:
      - master
      - main
      - dev
      - feature*
      - 'feature/**'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'

    - name: Install dependencies
      run: |
        ./config.sh

    - name: Build and Compile
      run: |
        python build.py -c -ninja standard-bs21e-1100e

    - name: Prepare artifact name
      id: artifact_name
      run: |
        BRANCH_NAME="${{ github.ref_name || github.head_ref || 'unknown' }}"
        SAFE_NAME=$(echo "$BRANCH_NAME" | sed 's/[\/\\:*?"<>|]/-/g')
        echo "name=standard-bs21e-1100e-$SAFE_NAME" >> $GITHUB_OUTPUT

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ steps.artifact_name.outputs.name }}
        path: output/bs21e/fwpkg/standard-bs21e-1100e