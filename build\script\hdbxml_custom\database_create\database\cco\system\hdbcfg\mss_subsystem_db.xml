﻿<?xml version="1.0" encoding="utf-8"?>
<!--CompanyNameMagicTag-->
<MSS>
  <!--The definition of MSS basic ID, including Category ID, Module ID and name. Category / Module ID between subsystems must be unique-->
  <SUBSYSTEM>
    <CATEGORY>
      <CAT NAME="System" ID="0x00000400" DESCRIPTION="System" />
      <CAT NAME="Print" ID="0x00008000" DESCRIPTION="Print" />
      <CAT NAME="Event" ID="0x00004000" DESCRIPTION="Event" />
      <CAT NAME="Air" ID="0x00002000" DESCRIPTION="Air" />
      <CAT NAME="Layer" ID="0x00001000" DESCRIPTION="Layer" />
      <CAT NAME="User Plane" ID="0x00000200" DESCRIPTION="UserPlane" />
      <CAT NAME="Message" ID="0x00000100" DESCRIPTION="Message" />
      <CAT NAME="Custom messages" ID="0x00000010" DESCRIPTION="Custom messages" />
    </CATEGORY>
    <TITLE>
      <ITEM ID="0x00" NAME="NO." STAT="1" VALUE="0" DESCRIPTION="..." />
      <ITEM ID="0x01" NAME="Category" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x02" STAT="0" NAME="Level" DESCRIPTION="..." />
      <ITEM ID="0x03" STAT="1" NAME="Date" DESCRIPTION="..." />
      <ITEM ID="0x04" NAME="Time" STAT="1" DESCRIPTION="..." />
      <ITEM ID="0x05" NAME="TimeStamp" STAT="1" DESCRIPTION="..." />
      <ITEM ID="0x06" NAME="Chip" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x07" NAME="Cpu" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x08" NAME="Side" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x09" NAME="Layer" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x0A" NAME="Prim" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x0B" NAME="Source" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x0C" NAME="Destination" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x0D" NAME="FileName" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x0E" NAME="LineNo" STAT="0" DESCRIPTION="..." />
      <ITEM ID="0x0F" NAME="Data" STAT="0" DESCRIPTION="..." />
    </TITLE>
    <LEVEL>
      <PRINT ICON="\logging_item_type_icon.png">
        <LEVEL NAME="Error" ID="0x40000000" ICON_OFFSET="0x1" DESCRIPTION="Error" />
        <LEVEL NAME="Warning" ID="0x20000000" ICON_OFFSET="0x0" DESCRIPTION="Warning" />
        <LEVEL NAME="Info" ID="0x10000000" ICON_OFFSET="0x2" DESCRIPTION="Info" />
        <LEVEL NAME="Normal" ID="0x08000000" ICON_OFFSET="0x4" DESCRIPTION="Normal" />
      </PRINT>
    </LEVEL>
    <MODULES RANGE="0x001,0xFFFF">
      <MOD ID="0x00000100" NAME="INVALID" DESCRIPTION="INVALID" />
      <MOD ID="0x00000101" NAME="DSP" DESCRIPTION="DSP" />
      <MOD ID="0x00000102" NAME="LL1" DESCRIPTION="LL1" />
      <MOD ID="0x00000103" NAME="L2_UL" DESCRIPTION="L2_UL" />
      <MOD ID="0x00000104" NAME="L2_DL" DESCRIPTION="L2_DL" />
      <MOD ID="0x00000105" NAME="MAC_DL" DESCRIPTION="MAC_DL" />
      <MOD ID="0x00000106" NAME="MAC_UL" DESCRIPTION="MAC_UL" />
      <MOD ID="0x00000107" NAME="RLC_UL" DESCRIPTION="RLC_UL" />
      <MOD ID="0x00000108" NAME="RLC_DL" DESCRIPTION="RLC_DL" />
      <MOD ID="0x00000109" NAME="PDCP" DESCRIPTION="PDCP" />
      <MOD ID="0x0000010a" NAME="RRC" DESCRIPTION="RRC" />
      <MOD ID="0x0000010b" NAME="EMMSM" DESCRIPTION="EMMSM" />
      <MOD ID="0x0000010c" NAME="MN" DESCRIPTION="MN" />
      <MOD ID="0x0000010d" NAME="AT" DESCRIPTION="AT" />
      <MOD ID="0x0000010e" NAME="PDH" DESCRIPTION="PDH" />
      <MOD ID="0x0000010f" NAME="LWIP" DESCRIPTION="LWIP" />
      <MOD ID="0x00000110" NAME="SIM" DESCRIPTION="SIM" />
      <MOD ID="0x00000111" NAME="LOG" DESCRIPTION="LOG" />
      <MOD ID="0x00000112" NAME="MONITOR" DESCRIPTION="MONITOR" />
      <MOD ID="0x00000113" NAME="HOSTTEST_RF" DESCRIPTION="HOSTTEST_RF" />
      <MOD ID="0x00000114" NAME="HOSTTEST_TX" DESCRIPTION="HOSTTEST_TX" />
      <MOD ID="0x00000115" NAME="HOSTTEST_RX" DESCRIPTION="HOSTTEST_RX" />
      <MOD ID="0x00000116" NAME="NVCONFIG" DESCRIPTION="NVCONFIG" />
      <MOD ID="0x00000117" NAME="NAS" DESCRIPTION="NAS" />
      <MOD ID="0x00000118" NAME="IRMALLOC" DESCRIPTION="IRMALLOC" />
      <MOD ID="0x00000119" NAME="PROTO" DESCRIPTION="PROTO" />
      <MOD ID="0x0000011a" NAME="SMS" DESCRIPTION="SMS" />
      <MOD ID="0x0000011b" NAME="LPP" DESCRIPTION="LPP" />
      <MOD ID="0x0000011c" NAME="ROHC" DESCRIPTION="ROHC" />
      <MOD ID="0x0000011d" NAME="UICC" DESCRIPTION="UICC" />
      <MOD ID="0x0000011e" NAME="UE" DESCRIPTION="UE" />
      <MOD ID="0x0000011f" NAME="BLE" DESCRIPTION="BLE" />
      <MOD ID="0x00000120" NAME="RF" DESCRIPTION="RF" />
      <MOD ID="0x00000121" NAME="EMM" DESCRIPTION="EMM" />
      <MOD ID="0x00000122" NAME="ESM" DESCRIPTION="ESM" />
      <MOD ID="0x00000123" NAME="MMC" DESCRIPTION="MMC" />
      <MOD ID="0x00000124" NAME="TAF" DESCRIPTION="TAF" />
      <MOD ID="0x00000127" NAME="RADIO" DESCRIPTION="RADIO" />
      <MOD ID="0x00000201" NAME="APP_LWIP_SUPPORT" DESCRIPTION="APP_LWIP_SUPPORT" />
      <MOD ID="0x00000208" NAME="APP_AT" DESCRIPTION="APP_AT" />
      <MOD ID="0x00000214" NAME="APP_OTA" DESCRIPTION="APP_OTA" />
      <MOD ID="0x0000021C" NAME="APP_NETWORK" DESCRIPTION="APP_NETWORK" />
      <MOD ID="0x00000125" NAME="APP_ATINY" DESCRIPTION="APP_ATINY" />
      <MOD ID="0x00000126" NAME="APP_DTLS" DESCRIPTION="APP_DTLS" />
      <MOD ID="0x00000241" NAME="APP_LOG" DESCRIPTION="APP_LOG" />
    </MODULES>
  </SUBSYSTEM>
</MSS>