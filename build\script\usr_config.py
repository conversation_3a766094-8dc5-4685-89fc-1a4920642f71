#!/usr/bin/env python
# -*- coding: UTF-8 -*-
# Copyright (c) @CompanyNameMagicTag 2023-2023. All rights reserved.

import os, sys, shutil
import argparse
from kconfiglib import Kconfig
from menuconfig import menuconfig

root_path = os.path.join(os.path.dirname(os.path.abspath(__file__)) + os.path.sep + ".", "..", "..")

def mconf_set_env(style, conf, config_header, autoheader_header, output_path = None):
    """
    Set Kconfig Env
    """
    os.environ["MENUCONFIG_STYLE"] = style
    os.environ["KCONFIG_CONFIG"] = conf
    os.environ["KCONFIG_CONFIG_HEADER"] = config_header
    os.environ["KCONFIG_AUTOHEADER"] = os.path.join("." if output_path == None else output_path, "mconfig.h")
    os.environ["KCONFIG_AUTOHEADER_HEADER"] = autoheader_header
    os.environ["CONFIG_"] = "CONFIG_"

def mconfig(command, chip, core, target, output, assignments = None, root=""):
    if not root:
        root = root_path

    kconfig = os.path.join(root, "config.in")
    print(kconfig)
    print(os.getcwd())

    display_style = "default selection=fg:white,bg:blue"
    target = target.replace('-', "_")
    target_conf = os.path.join(root, "build", "config", "target_config", chip, "menuconfig", core, "%s.config" % target)
    config_header = '''# Generated by Kconfig Tool.
# Note: !!!This file can not be modify manually!!!
'''
    autoheader_header = '''/* Copyright (c) @CompanyNameMagicTag 2022-2022. All rights reserved. */
'''
    mconf_set_env(display_style, target_conf, config_header, autoheader_header, output)
    kconf = Kconfig(filename=kconfig)

    if command == 'savemenuconfig':
        kconf.load_config()
        print(kconf.write_autoconf()) # save menu config
    elif command == 'defconfig':
        kconf.load_allconfig("alldef.config")
        print(kconf.write_config()) # default config
    elif command == 'allyesconfig':
        kconf.warn = False
        for sym in kconf.unique_defined_syms:
            sym.set_value(1 if sym.choice else 2)
        for choice in kconf.unique_choices:
            choice.set_value(2)
        kconf.warn = True
        kconf.load_allconfig("allyes.config")
        print(kconf.write_config()) # all yes config
    elif command == 'allnoconfig':
        kconf.warn = False
        for sym in kconf.unique_defined_syms:
            sym.set_value(2 if sym.is_allnoconfig_y else 0)
        kconf.warn = True
        kconf.load_allconfig("allno.config")
        print(kconf.write_config()) # all no config
    elif command == 'setconfig':
        kconf.load_config()
        for arg in args.assignments:
            if "=" not in arg:
                sys.exit("error: no '=' in assignment: '{}'".format(arg))
            name, value = arg.split("=", 1)

            sym = kconf.syms[name]

            if not sym.set_value(value):
                sys.exit("error: '{}' is an invalid value for the {} symbol {}"
                        .format(value, kconfiglib.TYPE_TO_STR[sym.orig_type],
                                name))

        print(kconf.write_config())
    elif command == 'reloadconfig':
        kconf.load_config()
        print(kconf.write_config())
    else:
        menuconfig(kconf)   # menu config

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)

    parser.add_argument(
        "--command",
        default="menuconfig",
        choices=["menuconfig", "savemenuconfig", "defconfig", "allyesconfig", "allnoconfig", "setconfig", "openproject"],
        help="command to be used")

    parser.add_argument(
        "--chip",
        default="bs25",
        help="chips: bs25/bs21/ws63/ws53 ...")

    parser.add_argument(
        "--core",
        default="acore",
        help="cores: acore/bcore/ccore ...")

    parser.add_argument(
        "--target",
        default="standard-bs25-app-evb",
        help="targets: standard-bs25-app-evb ...")

    parser.add_argument(
        "--output",
        help="The path of the mconfig.h file will be stored.")

    parser.add_argument(
        "assignments",
        metavar="ASSIGNMENT",
        nargs="*",
        help="A 'NAME=value' assignment")

    args = parser.parse_args()

    if args.command == "openproject":
        if os.path.exists("board.json"):
            if os.path.exists(root_path + "/board.json"):
                root_path + "/board.json"
                pass
            shutil.copyfile("board.json", root_path + "/board.json")

        current_path = os.getcwd()
        current_sample = "SAMPLE_SUPPORT_" + os.path.basename(current_path).upper()
        os.chdir(root_path)
        args.command = "setconfig"
        args.assignments.clear()
        args.assignments.append("SAMPLE_ENABLE=n")
        mconfig(args.command, args.chip, args.core, args.target, args.output, args.assignments, root_path)
        args.assignments.clear()
        args.assignments.append("SAMPLE_ENABLE=y")
        if "peripheral" in current_path:
            args.assignments.append("ENABLE_PERIPHERAL_SAMPLE=y")
        elif "products" in current_path:
            args.assignments.append("ENABLE_PRODUCTS_SAMPLE=y")
        else:
            print("Not a valid sample directory")
            exit(1)
        args.assignments.append(current_sample + "=y")



    mconfig(args.command, args.chip, args.core, args.target, args.output, args.assignments, root_path)