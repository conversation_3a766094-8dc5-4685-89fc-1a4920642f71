/*
 * Copyright (c) @CompanyNameMagicTag 2020-2020. All rights reserved.
 * File          ux_box_log.h
 * Description:  Audio ux box log code define and log interface
 */

#ifndef UX_BOX_LOG_H
#define UX_BOX_LOG_H


typedef enum {
    UX_BOX_LOG_NUM_0 = 0,
    UX_BOX_LOG_NUM_1,
    UX_BOX_LOG_NUM_2,
    UX_BOX_LOG_NUM_3,
    UX_BOX_LOG_NUM_4,
    UX_BOX_LOG_NUM_5,
    UX_BOX_LOG_NUM_6,
    UX_BOX_LOG_NUM_7,
    UX_BOX_LOG_NUM_8,
    UX_BOX_LOG_NUM_9,
    UX_BOX_LOG_NUM_10,
    UX_BOX_LOG_NUM_11,
    UX_BOX_LOG_NUM_12,
    UX_BOX_LOG_NUM_13,
    UX_BOX_LOG_NUM_14,
    UX_BOX_LOG_NUM_15,
    UX_BOX_LOG_NUM_16,
    UX_BOX_LOG_NUM_17,
    UX_BOX_LOG_NUM_18,
    UX_BOX_LOG_NUM_19,
    UX_BOX_LOG_NUM_20,
    UX_BOX_LOG_NUM_21,
    UX_BOX_LOG_NUM_22,
    UX_BOX_LOG_NUM_23,
    UX_BOX_LOG_NUM_24,
    UX_BOX_LOG_NUM_25,
    UX_BOX_LOG_NUM_26,
    UX_BOX_LOG_NUM_27,
    UX_BOX_LOG_NUM_28,
    UX_BOX_LOG_NUM_29,
    UX_BOX_LOG_NUM_30,
    UX_BOX_LOG_NUM_31,
    UX_BOX_LOG_NUM_32,
    UX_BOX_LOG_NUM_33,
    UX_BOX_LOG_NUM_34,
    UX_BOX_LOG_NUM_35,
    UX_BOX_LOG_NUM_36,
    UX_BOX_LOG_NUM_37,
    UX_BOX_LOG_NUM_38,
    UX_BOX_LOG_NUM_39,
    UX_BOX_LOG_NUM_40,
    UX_BOX_LOG_NUM_41,
    UX_BOX_LOG_NUM_42,
    UX_BOX_LOG_NUM_43,
    UX_BOX_LOG_NUM_44,
    UX_BOX_LOG_NUM_45,
    UX_BOX_LOG_NUM_46,
    UX_BOX_LOG_NUM_47,
    UX_BOX_LOG_NUM_48,
    UX_BOX_LOG_NUM_49,
    UX_BOX_LOG_NUM_50,
    UX_BOX_LOG_NUM_51,
    UX_BOX_LOG_NUM_52,
    UX_BOX_LOG_NUM_53,
    UX_BOX_LOG_NUM_54,
    UX_BOX_LOG_NUM_55,
    UX_BOX_LOG_NUM_56,
    UX_BOX_LOG_NUM_57,
    UX_BOX_LOG_NUM_58,
    UX_BOX_LOG_NUM_59,
    UX_BOX_LOG_NUM_60,
    UX_BOX_LOG_NUM_61,
    UX_BOX_LOG_NUM_62,
    UX_BOX_LOG_NUM_63,
    UX_BOX_LOG_NUM_64,
    UX_BOX_LOG_NUM_65,
    UX_BOX_LOG_NUM_66,
    UX_BOX_LOG_NUM_67,
    UX_BOX_LOG_NUM_68,
    UX_BOX_LOG_NUM_69,
    UX_BOX_LOG_NUM_70,
    UX_BOX_LOG_NUM_71,
    UX_BOX_LOG_NUM_72,
    UX_BOX_LOG_NUM_73,
    UX_BOX_LOG_NUM_74,
    UX_BOX_LOG_NUM_75,
    UX_BOX_LOG_NUM_76,
    UX_BOX_LOG_NUM_77,
    UX_BOX_LOG_NUM_78,
    UX_BOX_LOG_NUM_79,
    UX_BOX_LOG_NUM_80,
    UX_BOX_LOG_NUM_81,
    UX_BOX_LOG_NUM_82,
    UX_BOX_LOG_NUM_83,
    UX_BOX_LOG_NUM_84,
    UX_BOX_LOG_NUM_85,
    UX_BOX_LOG_NUM_86,
    UX_BOX_LOG_NUM_87,
    UX_BOX_LOG_NUM_88,
    UX_BOX_LOG_NUM_89,
    UX_BOX_LOG_NUM_90,
    UX_BOX_LOG_NUM_91,
    UX_BOX_LOG_NUM_92,
    UX_BOX_LOG_NUM_93,
    UX_BOX_LOG_NUM_94,
    UX_BOX_LOG_NUM_95,
    UX_BOX_LOG_NUM_96,
    UX_BOX_LOG_NUM_97,
    UX_BOX_LOG_NUM_98,
    UX_BOX_LOG_NUM_99,
    UX_BOX_LOG_NUM_100,
    UX_BOX_LOG_NUM_101,
    UX_BOX_LOG_NUM_102,
    UX_BOX_LOG_NUM_103,
    UX_BOX_LOG_NUM_104,
    UX_BOX_LOG_NUM_105,
    UX_BOX_LOG_NUM_106,
    UX_BOX_LOG_NUM_107,
    UX_BOX_LOG_NUM_108,
    UX_BOX_LOG_NUM_109,
    UX_BOX_LOG_NUM_110,
    UX_BOX_LOG_NUM_111,
    UX_BOX_LOG_NUM_112,
    UX_BOX_LOG_NUM_113,
    UX_BOX_LOG_NUM_114,
    UX_BOX_LOG_NUM_115,
    UX_BOX_LOG_NUM_116,
    UX_BOX_LOG_NUM_117,
    UX_BOX_LOG_NUM_118,
    UX_BOX_LOG_NUM_119,
    UX_BOX_LOG_NUM_120,
    UX_BOX_LOG_NUM_121,
    UX_BOX_LOG_NUM_122,
    UX_BOX_LOG_NUM_123,
    UX_BOX_LOG_NUM_124,
    UX_BOX_LOG_NUM_125,
    UX_BOX_LOG_NUM_126,
    UX_BOX_LOG_NUM_127,
    UX_BOX_LOG_NUM_128,
    UX_BOX_LOG_NUM_129,
    UX_BOX_LOG_NUM_130,
    UX_BOX_LOG_NUM_131,
    UX_BOX_LOG_NUM_132,
    UX_BOX_LOG_NUM_133,
    UX_BOX_LOG_NUM_134,
    UX_BOX_LOG_NUM_135,
    UX_BOX_LOG_NUM_136,
    UX_BOX_LOG_NUM_137,
    UX_BOX_LOG_NUM_138,
    UX_BOX_LOG_NUM_139,
    UX_BOX_LOG_NUM_140,
    UX_BOX_LOG_NUM_141,
    UX_BOX_LOG_NUM_142,
    UX_BOX_LOG_NUM_143,
    UX_BOX_LOG_NUM_144,
    UX_BOX_LOG_NUM_145,
    UX_BOX_LOG_NUM_146,
    UX_BOX_LOG_NUM_147,
    UX_BOX_LOG_NUM_148,
    UX_BOX_LOG_NUM_149,
    UX_BOX_LOG_NUM_150,
    UX_BOX_LOG_NUM_151,
    UX_BOX_LOG_NUM_152,
    UX_BOX_LOG_NUM_153,
    UX_BOX_LOG_NUM_154,
    UX_BOX_LOG_NUM_155,
    UX_BOX_LOG_NUM_156,
    UX_BOX_LOG_NUM_157,
    UX_BOX_LOG_NUM_158,
    UX_BOX_LOG_NUM_159,
    UX_BOX_LOG_NUM_160,
    UX_BOX_LOG_NUM_161,
    UX_BOX_LOG_NUM_162,
    UX_BOX_LOG_NUM_163,
    UX_BOX_LOG_NUM_164,
    UX_BOX_LOG_NUM_165,
    UX_BOX_LOG_NUM_166,
    UX_BOX_LOG_NUM_167,
    UX_BOX_LOG_NUM_168,
    UX_BOX_LOG_NUM_169,
    UX_BOX_LOG_NUM_170,
    UX_BOX_LOG_NUM_171,
    UX_BOX_LOG_NUM_172,
    UX_BOX_LOG_NUM_173,
    UX_BOX_LOG_NUM_174,
    UX_BOX_LOG_NUM_175,
    UX_BOX_LOG_NUM_176,
    UX_BOX_LOG_NUM_177,
    UX_BOX_LOG_NUM_178,
    UX_BOX_LOG_NUM_179,
    UX_BOX_LOG_NUM_180,
    UX_BOX_LOG_NUM_181,
    UX_BOX_LOG_NUM_182,
    UX_BOX_LOG_NUM_183,
    UX_BOX_LOG_NUM_184,
    UX_BOX_LOG_NUM_185,
    UX_BOX_LOG_NUM_186,
    UX_BOX_LOG_NUM_187,
    UX_BOX_LOG_NUM_188,
    UX_BOX_LOG_NUM_189,
    UX_BOX_LOG_NUM_190,
    UX_BOX_LOG_NUM_191,
    UX_BOX_LOG_NUM_192,
    UX_BOX_LOG_NUM_193,
    UX_BOX_LOG_NUM_194,
    UX_BOX_LOG_NUM_195,
    UX_BOX_LOG_NUM_196,
    UX_BOX_LOG_NUM_197,
    UX_BOX_LOG_NUM_198,
    UX_BOX_LOG_NUM_199,
    UX_BOX_LOG_NUM_200,
    UX_BOX_LOG_NUM_201,
    UX_BOX_LOG_NUM_202,
    UX_BOX_LOG_NUM_203,
    UX_BOX_LOG_NUM_204,
    UX_BOX_LOG_NUM_205,
    UX_BOX_LOG_NUM_206,
    UX_BOX_LOG_NUM_207,
    UX_BOX_LOG_NUM_208,
    UX_BOX_LOG_NUM_209,
    UX_BOX_LOG_NUM_210,
    UX_BOX_LOG_NUM_211,
    UX_BOX_LOG_NUM_212,
    UX_BOX_LOG_NUM_213,
    UX_BOX_LOG_NUM_214,
    UX_BOX_LOG_NUM_215,
    UX_BOX_LOG_NUM_216,
    UX_BOX_LOG_NUM_217,
    UX_BOX_LOG_NUM_218,
    UX_BOX_LOG_NUM_219,
    UX_BOX_LOG_NUM_220,
    UX_BOX_LOG_NUM_221,
    UX_BOX_LOG_NUM_222,
    UX_BOX_LOG_NUM_223,
    UX_BOX_LOG_NUM_224,
    UX_BOX_LOG_NUM_225,
    UX_BOX_LOG_NUM_226,
    UX_BOX_LOG_NUM_227,
    UX_BOX_LOG_NUM_228,
    UX_BOX_LOG_NUM_229,
} ux_box_log_num_t;

#endif
