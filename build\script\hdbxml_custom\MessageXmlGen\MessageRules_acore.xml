<?xml version="1.0" encoding="utf-8" ?>
<RulesImport>
<!--
    All 'target' paths are relative to the parent of the 'source'. The 'source' is the field whose value is used
    to control the visibility or length of the 'target'. 

    The source field will be implicitly matched using the UEMonitor type filter '**' to allow us to handle arrays
    or unions. 

    A few examples:

      <LengthRule Message="MSG"     Array="data"  Length="len"  IsVariable="true"/>
      MSG.a.data would be controlled by MSG.a.len
      MSG.b[4].data would be controlled by MSG.b[4].len

      <LengthRule Message="MSG"     Array="sub.data"  Length="len"  IsVariable="true"/>
      MSG.a.sub.data would be controlled by MSG.a.len
      MSG.b[4].sub.data would be controlled by MSG.b[4].len

    This might result in unwanted matches. We can disambiguate this by using the fully qualified name to indicate an
    absolute path on the source.

      <LengthRule Message="MSG"     Array="MSG.data"  Length="MSG.a.len"  IsVariable="true"/>
      MSG.a.data would be controlled by MSG.a.len
      MSG.a.b.data would NOT be controlled by MSG.a.len or MSG.a.b.len


    We differentiate between the "length of simple type array" and "length of array of (structures or arrays)" constraints.
    This is done by extending the syntax of the target path using [*] for the latter.

      <LengthRule Message="LL1_IDLE_MEAS_CONFIG_REQ"  Array="intra_black_cells[*]"  Length="num_intra_black_cells"/>
-->

<!-- 
    Note on the use of the IsVariable attribute.

    Structures that are dynamically allocated to be different sizes and tend to have a uint8 [1] at the end of the definition
    are 'truly' variable length structures. These must have IsVariable attribute present and set. The Length field indicates
    how the size of the array, all elements of which are in use.

    Arrays that are a constant size within a structure and not deemed to be variable length, and so do NOT have the IsVariable
    attribute. The Length field indicates how many of the array elements are present and in use.
-->

<!--  Note: LengthRules must be before the UnionRules and then the ValidRules -->

<!-- Message logging using dynamically sized messages, so IsVariable is set. -->
    <Rules>
        <LengthRule Array="message_data" IsVariable="true" Length="length" Message="APP_AT_LOG_MESSAGE_CMD_ID"      />
        <LengthRule Array="message_data" IsVariable="true" Length="length" Message="APP_AT_LOG_MESSAGE_RESPONSE_ID" />
        <LengthRule Array="data" IsVariable="true" Length="length" Message="SOC_LWM2M_LOG_MESSAGE_STRING_ID"       />
        <LengthRule Array="data" IsVariable="true" Length="length" Message="SOC_MBEDTLS_LOG_MESSAGE_STRING_ID"     />
        <LengthRule Array="data" IsVariable="true" Length="length" Message="SOC_COAP_LOG_MESSAGE_STRING_ID"        />
        <LengthRule Array="data" IsVariable="true" Length="length" Message="LWIP_LOG_MESSAGE_STRING_ID"             />
        <LengthRule Array="data" IsVariable="true" Length="length" Message="SOC_ATINY_LOG_MESSAGE_STRING_ID"       />
        <LengthRule Array="message_data" IsVariable="true" Length="length" Message="APP_AT_LOG_MESSAGE_COMMON_ID"   />

<!-- Autogenerated by json_at_code.py from JSON AT commands. Do not edit. -->
        <UnionRule Message="APP_AT_LOG_MESSAGE_CMD_ID"  Union="args"  Selector="cmd_id">
            <UnionSelection Selection="CMD_CCHC" Option="cchc"/>
            <UnionSelection Selection="CMD_CCHO" Option="ccho"/>
            <UnionSelection Selection="CMD_CCIOTOPT" Option="cciotopt"/>
            <UnionSelection Selection="CMD_CCLK" Option="cclk"/>
            <UnionSelection Selection="CMD_CEDRXRDP" Option="cedrxrdp"/>
            <UnionSelection Selection="CMD_CEDRXS" Option="cedrxs"/>
            <UnionSelection Selection="CMD_CEER" Option="ceer"/>
            <UnionSelection Selection="CMD_CEREG" Option="cereg"/>
            <UnionSelection Selection="CMD_CFUN" Option="cfun"/>
            <UnionSelection Selection="CMD_CGACT" Option="cgact"/>
            <UnionSelection Selection="CMD_CGAPNRC" Option="cgapnrc"/>
            <UnionSelection Selection="CMD_CGATT" Option="cgatt"/>
            <UnionSelection Selection="CMD_CGAUTH" Option="cgauth"/>
            <UnionSelection Selection="CMD_CGCMOD" Option="cgcmod"/>
            <UnionSelection Selection="CMD_CGCONTRDP" Option="cgcontrdp"/>
            <UnionSelection Selection="CMD_CGDATA" Option="cgdata"/>
            <UnionSelection Selection="CMD_CGDCONT" Option="cgdcont"/>
            <UnionSelection Selection="CMD_CGEQOS" Option="cgeqos"/>
            <UnionSelection Selection="CMD_CGLA" Option="cgla"/>
            <UnionSelection Selection="CMD_CGMI" Option="cgmi"/>
            <UnionSelection Selection="CMD_CGMM" Option="cgmm"/>
            <UnionSelection Selection="CMD_CGMR" Option="cgmr"/>
            <UnionSelection Selection="CMD_CGPADDR" Option="cgpaddr"/>
            <UnionSelection Selection="CMD_CGSN" Option="cgsn"/>
            <UnionSelection Selection="CMD_CGTFT" Option="cgtft"/>
            <UnionSelection Selection="CMD_CIMI" Option="cimi"/>
            <UnionSelection Selection="CMD_CIPCA" Option="cipca"/>
            <UnionSelection Selection="CMD_CLAC" Option="clac"/>
            <UnionSelection Selection="CMD_CMEE" Option="cmee"/>
            <UnionSelection Selection="CMD_CMGC" Option="cmgc"/>
            <UnionSelection Selection="CMD_CMGS" Option="cmgs"/>
            <UnionSelection Selection="CMD_CMMS" Option="cmms"/>
            <UnionSelection Selection="CMD_CNEC" Option="cnec"/>
            <UnionSelection Selection="CMD_CNMA" Option="cnma"/>
            <UnionSelection Selection="CMD_CNMPSD" Option="cnmpsd"/>
            <UnionSelection Selection="CMD_COPS" Option="cops"/>
            <UnionSelection Selection="CMD_CPIN" Option="cpin"/>
            <UnionSelection Selection="CMD_CPINR" Option="cpinr"/>
            <UnionSelection Selection="CMD_CPSDO" Option="cpsdo"/>
            <UnionSelection Selection="CMD_CPSMS" Option="cpsms"/>
            <UnionSelection Selection="CMD_CRSM" Option="crsm"/>
            <UnionSelection Selection="CMD_CRTDCP" Option="crtdcp"/>
            <UnionSelection Selection="CMD_CSCA" Option="csca"/>
            <UnionSelection Selection="CMD_CSCON" Option="cscon"/>
            <UnionSelection Selection="CMD_CSIM" Option="csim"/>
            <UnionSelection Selection="CMD_CSMS" Option="csms"/>
            <UnionSelection Selection="CMD_CSODCP" Option="csodcp"/>
            <UnionSelection Selection="CMD_CSQ" Option="csq"/>
            <UnionSelection Selection="CMD_CTZR" Option="ctzr"/>
            <UnionSelection Selection="CMD_NATSPEED" Option="natspeed"/>
            <UnionSelection Selection="CMD_NBAND" Option="nband"/>
            <UnionSelection Selection="CMD_NBIPR" Option="nbipr"/>
            <UnionSelection Selection="CMD_NCACHE" Option="ncache"/>
            <UnionSelection Selection="CMD_NCALTEMPSENSOR" Option="ncaltempsensor"/>
            <UnionSelection Selection="CMD_NCCID" Option="nccid"/>
            <UnionSelection Selection="CMD_NCHIPINFO" Option="nchipinfo"/>
            <UnionSelection Selection="CMD_NCIDSTATUS" Option="ncidstatus"/>
            <UnionSelection Selection="CMD_NCONFIG" Option="nconfig"/>
            <UnionSelection Selection="CMD_NCPCDPR" Option="ncpcdpr"/>
            <UnionSelection Selection="CMD_NCSEARFCN" Option="ncsearfcn"/>
            <UnionSelection Selection="CMD_NDEBUG" Option="ndebug"/>
            <UnionSelection Selection="CMD_NDIEID" Option="ndieid"/>
            <UnionSelection Selection="CMD_NEARFCN" Option="nearfcn"/>
            <UnionSelection Selection="CMD_NFWUPD" Option="nfwupd"/>
            <UnionSelection Selection="CMD_NGACTR" Option="ngactr"/>
            <UnionSelection Selection="CMD_NGSEARFCN" Option="ngsearfcn"/>
            <UnionSelection Selection="CMD_NGT3412" Option="ngt3412"/>
            <UnionSelection Selection="CMD_NIPINFO" Option="nipinfo"/>
            <UnionSelection Selection="CMD_NITZ" Option="nitz"/>
            <UnionSelection Selection="CMD_NLOGLEVEL" Option="nloglevel"/>
            <UnionSelection Selection="CMD_NPIN" Option="npin"/>
            <UnionSelection Selection="CMD_NPING" Option="nping"/>
            <UnionSelection Selection="CMD_NPOWERCLASS" Option="npowerclass"/>
            <UnionSelection Selection="CMD_NPRDEFLASH" Option="nprdeflash"/>
            <UnionSelection Selection="CMD_NPSMR" Option="npsmr"/>
            <UnionSelection Selection="CMD_NPTWEDRXS" Option="nptwedrxs"/>
            <UnionSelection Selection="CMD_NQPULD" Option="nqpuld"/>
            <UnionSelection Selection="CMD_NQSOS" Option="nqsos"/>
            <UnionSelection Selection="CMD_NRB" Option="nrb"/>
            <UnionSelection Selection="CMD_NRDCTRL" Option="nrdctrl"/>
            <UnionSelection Selection="CMD_NRDEXEC" Option="nrdexec"/>
            <UnionSelection Selection="CMD_NRDMIPI" Option="nrdmipi"/>
            <UnionSelection Selection="CMD_NRDSET" Option="nrdset"/>
            <UnionSelection Selection="CMD_NRDTEST" Option="nrdtest"/>
            <UnionSelection Selection="CMD_NRNPDM" Option="nrnpdm"/>
            <UnionSelection Selection="CMD_NSESIM" Option="nsesim"/>
            <UnionSelection Selection="CMD_NSET" Option="nset"/>
            <UnionSelection Selection="CMD_NSNPD" Option="nsnpd"/>
            <UnionSelection Selection="CMD_NSOCL" Option="nsocl"/>
            <UnionSelection Selection="CMD_NSOCO" Option="nsoco"/>
            <UnionSelection Selection="CMD_NSOCR" Option="nsocr"/>
            <UnionSelection Selection="CMD_NSORF" Option="nsorf"/>
            <UnionSelection Selection="CMD_NSOSD" Option="nsosd"/>
            <UnionSelection Selection="CMD_NSOST" Option="nsost"/>
            <UnionSelection Selection="CMD_NSOSTATUS" Option="nsostatus"/>
            <UnionSelection Selection="CMD_NTPERMID" Option="ntpermid"/>
            <UnionSelection Selection="CMD_NTSETID" Option="ntsetid"/>
            <UnionSelection Selection="CMD_NUESTATS" Option="nuestats"/>
            <UnionSelection Selection="CMD_NUICC" Option="nuicc"/>
            <UnionSelection Selection="CMD_NXLOG" Option="nxlog"/>
            <UnionSelection Selection="CMD_AT" Option=""/>
            <UnionSelection Selection="CMD_UNKNOWN" Option=""/>
            <UnionSelection Selection="CMD_DUMMY" Option="dummy"/>
        </UnionRule>
        <ValidRule Message="APP_AT_LOG_MESSAGE_CMD_ID"  Field="args"  Indicator="args_present"/>
<!-- Autogenerated. -->


    </Rules>
</RulesImport>
