UINT64  64      0
UINT32	32		0
UINT16	16		0
UINT8	8               0

INT64   64      1
INT32	32		1
INT16	16		1
INT8	8		1

enum	    32		0

u64         64      0
u32         32		0
u16         16		0
u8          8      0

s64    64   1
s32    32   1
s16    16	1
s8     8	1
BOOL   8	0

osal_u64        64   0
osal_u32		32	 0
osal_u16		16	 0
osal_u8		8	 0
osal_s64        64   1
osal_s32		32	 1
osal_s16		16	 1
osal_s8		8	 1
osal_bool		8	 0
osal_char     8    1  1
char        8    1  1

long	    32	 1
osal_pvoid    32	 0
osal_pbyte    32	 0

