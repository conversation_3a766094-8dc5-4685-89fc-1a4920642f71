#===============================================================================
# @brief    Kconfig file.
# Copyright (c) @CompanyNameMagicTag 2023-2023. All rights reserved.
#===============================================================================
config SLE_MTU_LENGTH
    int
    prompt "Set the length of mtu."
    default 300
    help
        This option means the length of mtu.

config SLE_MULTICON_NUM
    int
    prompt "Set the num of sle multicon."
    range 1 2
    default 1
    help
        This option means the num of sle multicon.

config SAMPLE_SUPPORT_SLE_RCU_TYPE
    bool
    prompt "Select SLE rcu type."
    default n
    help
        This option means the SLE type of rcu to select.

choice
    prompt "Select sle rcu type"
    depends on SAMPLE_SUPPORT_SLE_RCU_TYPE
    default SAMPLE_SUPPORT_SLE_RCU_SERVER
    config SAMPLE_SUPPORT_SLE_RCU_SERVER
        bool "Enable SLE RCU Server sample."
    config SAMPLE_SUPPORT_SLE_RCU_DONGLE
        bool "Enable SLE RCU Dongle sample."
        select DRIVERS_USB_UAC_HID_GADGET
        select DRIVERS_USB_HID_CUSTOM
endchoice

config SAMPLE_SUPPORT_BLE_RCU_SERVER
    bool
    prompt "Select BLE rcu type."
    default n
    help
        This option means the BLE type of rcu to select.

config SAMPLE_SUPPORT_IR
    bool
    select DRIVER_SUPPORT_IR
    select PWM_PRELOAD
    prompt "Select IR rcu type."
    default n
    help
        This option means the IR type of rcu to select.

if SLE_MULTICON_NUM = 1
    config SLE_MULTICON_SERVER1_ADDR0
        hex
        default 0x0A
        prompt "Set the target sle server1 addr[0] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR1
        hex
        default 0x01
        prompt "Set the target sle server1 addr[1] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR2
        hex
        default 0x02
        prompt "Set the target sle server1 addr[2] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR3
        hex
        default 0x03
        prompt "Set the target sle server1 addr[3] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR4
        hex
        default 0x04
        prompt "Set the target sle server1 addr[4] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR5
        hex
        default 0x05
        prompt "Set the target sle server1 addr[5] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
endif

if SLE_MULTICON_NUM = 2
    config SLE_EXSIT_TWO_MULTICON_SERVER
        int
        default 1
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
    config SLE_MULTICON_SERVER1_ADDR0
        hex
        default 0x0A
        prompt "Set the target sle server1 addr[0] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR1
        hex
        default 0x01
        prompt "Set the target sle server1 addr[1] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR2
        hex
        default 0x02
        prompt "Set the target sle server1 addr[2] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR3
        hex
        default 0x03
        prompt "Set the target sle server1 addr[3] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR4
        hex
        default 0x04
        prompt "Set the target sle server1 addr[4] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER1_ADDR5
        hex
        default 0x05
        prompt "Set the target sle server1 addr[5] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.

    config SLE_MULTICON_SERVER2_ADDR0
        hex
        default 0x0B
        prompt "Set the target sle server2 addr[0] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER2_ADDR1
        hex
        default 0x01
        prompt "Set the target sle server2 addr[1] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER2_ADDR2
        hex
        default 0x02
        prompt "Set the target sle server2 addr[2] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER2_ADDR3
        hex
        default 0x03
        prompt "Set the target sle server2 addr[3] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER2_ADDR4
        hex
        default 0x04
        prompt "Set the target sle server2 addr[4] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
    config SLE_MULTICON_SERVER2_ADDR5
        hex
        default 0x05
        prompt "Set the target sle server2 addr[5] of wanting to connect."
        depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
        help
            This option means the addr of tartget server wanting to connect.
endif

config SLE_MULTICON_SERVER_ADDR0
    hex
    default 0x0A
    prompt "Set the sle server addr[0]."
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER
config SLE_MULTICON_SERVER_ADDR1
    hex
    default 0x01
    prompt "Set the sle server addr[1]."
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER
config SLE_MULTICON_SERVER_ADDR2
    hex
    default 0x02
    prompt "Set the sle server addr[2]."
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER
config SLE_MULTICON_SERVER_ADDR3
    hex
    default 0x03
    prompt "Set the sle server addr[3]."
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER
config SLE_MULTICON_SERVER_ADDR4
    hex
    default 0x04
    prompt "Set the sle server addr[4]."
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER
config SLE_MULTICON_SERVER_ADDR5
    hex
    default 0x05
    prompt "Set the sle server addr[5]."
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER

config SLE_MULTICON_CLIENT_ADDR0
    hex
    default 0x0B
    prompt "Set the sle client addr[0]."
    depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
config SLE_MULTICON_CLIENT_ADDR1
    hex
    default 0x01
    prompt "Set the sle client addr[1]."
    depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
config SLE_MULTICON_CLIENT_ADDR2
    hex
    default 0x02
    prompt "Set the sle client addr[2]."
    depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
config SLE_MULTICON_CLIENT_ADDR3
    hex
    default 0x03
    prompt "Set the sle client addr[3]."
    depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
config SLE_MULTICON_CLIENT_ADDR4
    hex
    default 0x04
    prompt "Set the sle client addr[4]."
    depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE
config SLE_MULTICON_CLIENT_ADDR5
    hex
    default 0x05
    prompt "Set the sle client addr[5]."
    depends on SAMPLE_SUPPORT_SLE_RCU_DONGLE

config SLE_MULTICON_SERVER_NAME
    string
    prompt "set the name of sle server."
    default "sle_rcu_server1"
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER

config ADC_USE_PIN1
    int
    prompt "Choose ADC USE pin1."
    default 30
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER

config ADC_USE_PIN2
    int
    prompt "Choose ADC USE pin2."
    default 31
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER

config USB_UAC_MAX_RECORD_SIZE
    hex
    prompt "Set the max recorder size of USB UAC."
    default 0x400
    help
        This option means the max recorder size of USB UAC.
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER

config USB_PDM_TRANSFER_LEN_BY_DMA
    int
    prompt "Set the length of transfer by DMA."
    default 256
    help
        This option means the length of transfer by DMA.
    depends on SAMPLE_SUPPORT_SLE_RCU_SERVER

config RCU_MASS_PRODUCTION_TEST
    bool
    prompt "RCU mass prodution test."
    default n
    help
        This option means the support RCU mass prodution test.

config APP_LOG_ENABLE
    bool
    prompt "Determine whether to use app_print."
    default n
    help
        This option means the opening of app_print.