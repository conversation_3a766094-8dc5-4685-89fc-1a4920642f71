﻿<?xml version="1.0" encoding="utf-8"?>
<!--companytag Technologies Co.,Ltd.-->
<!--插件ID定义：DCL:0x100; ACL:0x101,CG:0x102,LV:0x110,SG:0x111,MV:0x112,DS:0x120,NVI:0x121,MS:0x122,LCM:0x123-->

<!--V1.0-->
<DebugKits>
  <GROUP NAME="AUTO" DATA_STRUCT_FILE="..\diag\apps_core_hso_msg_struct_def.txt" MULTIMODE="Firefly" PLUGIN="0x111,0x110(1),0x252">
  </GROUP>
  <GROUP NAME="FIX" DATA_STRUCT_FILE="..\diag\fix_struct_def.txt" MULTIMODE="Firefly" AUTO_STRUCT="YES" PLUGIN="0x111,0x110(1),0x252">
    <CMD ID="0x0905" NAME="get_cpup" DESCRIPTION="get_cpup" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="diag_cpup_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="dfx_cpup_item_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x0A05" NAME="get_mem_info" DESCRIPTION="get_mem_info" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="ext_mdm_mem_info" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x0B05" NAME="get_res_info" DESCRIPTION="get_res_info" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="os_resource_use_stat_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x0C05" NAME="get_task_info" DESCRIPTION="get_task_info" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="ext_task_info" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x0D05" NAME="mem32" DESCRIPTION="mem32" PLUGIN="0x100,0x102,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="mem_read_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="mem_read32_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x0E05" NAME="mem16" DESCRIPTION="mem16" PLUGIN="0x100,0x102,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="mem_read_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="mem_read16_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
     <CMD ID="0x0F05" NAME="mem8" DESCRIPTION="mem8" PLUGIN="0x100,0x102,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="mem_read_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="mem_read8_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
     <CMD ID="0x1005" NAME="w1" DESCRIPTION="w1" PLUGIN="0x100,0x102,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="mem_write_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="mem_write_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
     <CMD ID="0x1105" NAME="w2" DESCRIPTION="w2" PLUGIN="0x100,0x102,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="mem_write_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="mem_write_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
     <CMD ID="0x1205" NAME="w4" DESCRIPTION="w4" PLUGIN="0x100,0x102,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="mem_write_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="mem_write_ind_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x1505" NAME="last_word" DESCRIPTION="" PLUGIN="0x100,0x110" TYPE="IND">
      <IND STRUCTURE="diag_last_word_ind_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x3205" NAME="nv_read_with_attr" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="nv_read_input_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_read_with_attr_output_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x3305" NAME="nv_write_with_attr" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="nv_write_with_attr_input_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_write_with_attr_output_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x6A05" NAME="nv_get_store_status" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_store_status_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x6B05" NAME="nv_backup_keys" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x6C05" NAME="nv_restore_keys" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x6D05" NAME="nv_get_page_usage" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_page_usage_out" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x6E05" NAME="nv_set_restore_flag" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="nv_reset_model_in_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_reset_model_out_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x4205" NAME="uart_cfg" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="uart_attr_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_str_type" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x4305" NAME="get_uart_cfg" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="uart_attr_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x4405" NAME="mocked_shell_enable" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_u32" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_str" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x4505" NAME="offline_log_cfg" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_u32" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_str" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x4605" NAME="get_init_info" DESCRIPTION="get_init_info" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="dfx_sys_boot_recode_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x4a05" NAME="audio_proc_ai" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x4b05" NAME="audio_proc_sea" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x4c05" NAME="audio_proc_adec" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x4d05" NAME="audio_proc_aenc" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x4e05" NAME="audio_proc_ao" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x4f05" NAME="audio_proc_ab" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x5005" NAME="audio_proc_sys" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x5105" NAME="audio_dump" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="audio_dump_cmd_item" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x5205" NAME="audio_probe" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x5605" NAME="diag_dfx" DESCRIPTION="diag_test_cmd" PLUGIN="0x100,0x252" TYPE="REQ_IND">
      <REQ STRUCTURE="diag_dfx_cmd_req_st" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="diag_dfx_cmd_ind_st" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x5705" NAME="ind_diag_dfx_stat" DESCRIPTION="diag_test_cmd" PLUGIN="0x100,0x252" TYPE="IND">
      <IND STRUCTURE="zdiag_dfx_stat" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x5F05" NAME="stat_query" DESCRIPTION="diag_test_cmd" PLUGIN="0x100,0x252" TYPE="REQ">
      <REQ STRUCTURE="diag_dbg_stat_query_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x7005" NAME="upg_prepare" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="upg_prepare_info_t" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x7105" NAME="upg_request" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x7205" NAME="upg_start" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x7405" NAME="upg_get_size" DESCRIPTION="" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="tool_null_stru" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_u32" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x7605" NAME="logfile_cfg" DESCRIPTION="logfile_cfg" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="dfx_test_logfile_cfg_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_str" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x7705" NAME="logfile_ctrl" DESCRIPTION="logfile_ctrl" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="dfx_test_logfile_ctrl_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_str" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x7805" NAME="logfile_write" DESCRIPTION="logfile_write" PLUGIN="0x100,0x102" TYPE="REQ_IND">
      <REQ STRUCTURE="dfx_test_logfile_write_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_str" TYPE="Auto" RESULT_CODE="" />
    </CMD>
  </GROUP>
  <GROUP NAME="TOOL_FIX" DATA_STRUCT_FILE="..\diag\tool_fix_struct_def.txt" MULTIMODE="Firefly" PLUGIN="0x111,0x110(1),0x252">
    <CMD ID="0xff05" TYPE="IND" NAME="msg_sys_mini" PLUGIN="0x110(1)" DESCRIPTION="MSG上报（SYS）"></CMD>
    <CMD ID="0xfe05" TYPE="IND" NAME="msg_sys_full" PLUGIN="0x110(1)" DESCRIPTION="MSG上报（SYS）"></CMD>
    <CMD ID="0xfd05" TYPE="IND" NAME="msg_sys_normal" PLUGIN="0x110(1)" DESCRIPTION="MSG上报（SYS）"></CMD>
    <CMD ID="0xf905" TYPE="IND" NAME="msg_sys_extend" PLUGIN="0x110(1)" DESCRIPTION="MSG上报（SYS）"></CMD>
    <CMD ID="0x0706" NAME="get_file_list" DESCRIPTION="" PLUGIN="0x102(2),0x259" TYPE="REQ">
      <REQ STRUCTURE="GetFileListReq" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="diag_cmd_sal_sys_sdm" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0806" NAME="delete_file" DESCRIPTION="" PLUGIN="0x102(2),0x259" TYPE="REQ">
      <REQ STRUCTURE="ext_diag_del_cmd" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="ext_diag_del_ind" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0906" NAME="download_file" DESCRIPTION="" PLUGIN="0x102(2),0x259" TYPE="REQ">
      <REQ STRUCTURE="FileContentReq" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="diag_cmd_sal_sys_sdm" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0106" NAME="transmit_start" DESCRIPTION="" PLUGIN="0x102(2),0x259,0x261" TYPE="REQ">
      <REQ STRUCTURE="transmit_start_pkt_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_null_stru" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0206" NAME="transmit_negotiate" DESCRIPTION="" PLUGIN="0x102(2),0x259,0x261" TYPE="IND">
      <IND STRUCTURE="transmit_negotiate_pkt_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0306" NAME="transmit_request" DESCRIPTION="" PLUGIN="0x102(2),0x259,0x261" TYPE="REQ">
      <REQ STRUCTURE="transmit_data_request_item_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_null_stru" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0406" NAME="tranmit_reply" DESCRIPTION="" PLUGIN="0x259,0x261" TYPE="IND">
      <IND STRUCTURE="transmit_data_reply_pkt_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0506" NAME="transmit_state" DESCRIPTION="" PLUGIN="0x102(2),0x259,0x261" TYPE="REQ">
      <REQ STRUCTURE="transmit_state_notify_pkt" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_null_stru" TYPE="Auto" />
    </CMD>
    <CMD ID="0x0606" NAME="transmit_stop" DESCRIPTION="" PLUGIN="0x102(2),0x259,0x261" TYPE="REQ">
      <REQ STRUCTURE="transmit_stop_pkt_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_null_stru" TYPE="Auto" />
    </CMD>
    <CMD ID="0x4105" TYPE="IND" NAME="提示" PLUGIN="0x100,0x252,0x303" DESCRIPTION="">
      <IND STRUCTURE="str_type" TYPE="Auto" PARAM_VALUE="" />
    </CMD>
    <CMD ID="0x0405" NAME="diag_change_pwd" DESCRIPTION="diag_change_pwd" PLUGIN="0x100,0x259" TYPE="IND">
      <IND STRUCTURE="diag_cmd_change_pwd_ind_stru_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x1805" NAME="last_dump_start" DESCRIPTION="" PLUGIN="0x110,0x261" TYPE="IND">
      <IND STRUCTURE="last_dump_start_ind_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x1905" NAME="last_dump_end" DESCRIPTION="" PLUGIN="0x110,0x261" TYPE="IND">
      <IND STRUCTURE="last_dump_start_ind_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x1305" NAME="last_dump" DESCRIPTION="" PLUGIN="0x110,0x261" TYPE="IND">
      <IND STRUCTURE="last_dump_data_ind_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x1405" NAME="last_dump_finish" DESCRIPTION="" PLUGIN="0x110,0x261" TYPE="IND">
      <IND STRUCTURE="last_dump_data_ind_finish_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x2405" NAME="audio_aef_haid" DESCRIPTION="" PLUGIN="0x102(2),0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="iMediaHearingAidsParams" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="iMediaHearingAidsParams" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x2605" NAME="audio_aef_3a" DESCRIPTION="" PLUGIN="0x100,0x102(2),0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="audio_vqe_param_struct" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="audio_vqe_param_struct" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x2505" NAME="audio_aef_sws" DESCRIPTION="" PLUGIN="0x100,0x102(2),0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="audio_sws_mobile_para" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="audio_sws_mobile_para" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x3005" NAME="nv_read" DESCRIPTION="" PLUGIN="0x100,0x102(2),0x121" TYPE="REQ_IND">
      <REQ STRUCTURE="nv_read_input_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_read_output_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x3105" NAME="nv_write" DESCRIPTION="" PLUGIN="0x100,0x102(2),0x121" TYPE="REQ_IND">
      <REQ STRUCTURE="nv_write_input_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="nv_write_output_t" TYPE="Auto" RESULT_CODE="" />
    </CMD>
    <CMD ID="0x3905" NAME="sample_data" DESCRIPTION="" PLUGIN="0x100,0x259" TYPE="REQ_IND">
      <REQ STRUCTURE="diag_sample_data_cmd_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="diag_sample_data_ind_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x3d05" NAME="tranmit_reply" DESCRIPTION="" PLUGIN="0x259,0x261" TYPE="IND">
      <IND STRUCTURE="transmit_data_reply_pkt_t" TYPE="Auto" />
    </CMD>
    <CMD ID="0x8005" NAME="rftest" DESCRIPTION="" PLUGIN="0x100,0x102(2),0x259" TYPE="REQ">
      <REQ STRUCTURE="transmit_state_notify_pkt_t" TYPE="Auto" PARAM_VALUE="" />
      <IND STRUCTURE="tool_null_stru" TYPE="Auto" />
    </CMD>
  </GROUP>
</DebugKits>
