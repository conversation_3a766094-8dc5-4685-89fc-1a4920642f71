/*
 * Copyright (c) @CompanyNameMagicTag 2020-2020. All rights reserved.
 * File          ux_multiple_device_log.h
 * Description:  Audio ux multiple device log code define and log interface
 */

#ifndef __UX_MULTIPLE_DEVICE_LOG_H__
#define __UX_MULTIPLE_DEVICE_LOG_H__

#include "ha_common.h"

#define ux_multiple_device_print_err(log_num, fmt, args...)   ha_base_print_err(0, fmt, ##args)
#define ux_multiple_device_print_warn(log_num, fmt, args...)  ha_base_print_warn(0, fmt, ##args)
#define ux_multiple_device_print_info(log_num, fmt, args...)  ha_base_print_info(0, fmt, ##args)
#define ux_multiple_device_print_debug(log_num, fmt, args...) ha_base_print_debug(0, fmt, ##args)

/* log_num in ux multiple device module */
typedef enum {
    UX_MULTIPLE_DEVICE_LOG_NUM_0 = 0,
    UX_MULTIPLE_DEVICE_LOG_NUM_1,
    UX_MULTIPLE_DEVICE_LOG_NUM_2,
    UX_MULTIPLE_DEVICE_LOG_NUM_3,
    UX_MULTIPLE_DEVICE_LOG_NUM_4,
    UX_MULTIPLE_DEVICE_LOG_NUM_5,
    UX_MULTIPLE_DEVICE_LOG_NUM_6,
    UX_MULTIPLE_DEVICE_LOG_NUM_7,
    UX_MULTIPLE_DEVICE_LOG_NUM_8,
    UX_MULTIPLE_DEVICE_LOG_NUM_9,
    UX_MULTIPLE_DEVICE_LOG_NUM_10,
    UX_MULTIPLE_DEVICE_LOG_NUM_11,
    UX_MULTIPLE_DEVICE_LOG_NUM_12,
    UX_MULTIPLE_DEVICE_LOG_NUM_13,
    UX_MULTIPLE_DEVICE_LOG_NUM_14,
    UX_MULTIPLE_DEVICE_LOG_NUM_15,
    UX_MULTIPLE_DEVICE_LOG_NUM_16,
    UX_MULTIPLE_DEVICE_LOG_NUM_17,
    UX_MULTIPLE_DEVICE_LOG_NUM_18,
    UX_MULTIPLE_DEVICE_LOG_NUM_19,
    UX_MULTIPLE_DEVICE_LOG_NUM_20,
    UX_MULTIPLE_DEVICE_LOG_NUM_21,
    UX_MULTIPLE_DEVICE_LOG_NUM_22,
    UX_MULTIPLE_DEVICE_LOG_NUM_23,
    UX_MULTIPLE_DEVICE_LOG_NUM_24,
    UX_MULTIPLE_DEVICE_LOG_NUM_25,
    UX_MULTIPLE_DEVICE_LOG_NUM_26,
    UX_MULTIPLE_DEVICE_LOG_NUM_27,
    UX_MULTIPLE_DEVICE_LOG_NUM_28,
    UX_MULTIPLE_DEVICE_LOG_NUM_29,
    UX_MULTIPLE_DEVICE_LOG_NUM_30,
    UX_MULTIPLE_DEVICE_LOG_NUM_31,
    UX_MULTIPLE_DEVICE_LOG_NUM_32,
    UX_MULTIPLE_DEVICE_LOG_NUM_33,
    UX_MULTIPLE_DEVICE_LOG_NUM_34,
    UX_MULTIPLE_DEVICE_LOG_NUM_35,
    UX_MULTIPLE_DEVICE_LOG_NUM_36,
    UX_MULTIPLE_DEVICE_LOG_NUM_37,
    UX_MULTIPLE_DEVICE_LOG_NUM_38,
    UX_MULTIPLE_DEVICE_LOG_NUM_39,
    UX_MULTIPLE_DEVICE_LOG_NUM_40,
    UX_MULTIPLE_DEVICE_LOG_NUM_41,
    UX_MULTIPLE_DEVICE_LOG_NUM_42,
    UX_MULTIPLE_DEVICE_LOG_NUM_43,
    UX_MULTIPLE_DEVICE_LOG_NUM_44,
    UX_MULTIPLE_DEVICE_LOG_NUM_45,
    UX_MULTIPLE_DEVICE_LOG_NUM_46,
    UX_MULTIPLE_DEVICE_LOG_NUM_47,
    UX_MULTIPLE_DEVICE_LOG_NUM_48,
    UX_MULTIPLE_DEVICE_LOG_NUM_49,
    UX_MULTIPLE_DEVICE_LOG_NUM_50,
    UX_MULTIPLE_DEVICE_LOG_NUM_51,
    UX_MULTIPLE_DEVICE_LOG_NUM_52,
    UX_MULTIPLE_DEVICE_LOG_NUM_53,
    UX_MULTIPLE_DEVICE_LOG_NUM_54,
    UX_MULTIPLE_DEVICE_LOG_NUM_55,
    UX_MULTIPLE_DEVICE_LOG_NUM_56,
    UX_MULTIPLE_DEVICE_LOG_NUM_57,
    UX_MULTIPLE_DEVICE_LOG_NUM_58,
    UX_MULTIPLE_DEVICE_LOG_NUM_59,
    UX_MULTIPLE_DEVICE_LOG_NUM_60,
    UX_MULTIPLE_DEVICE_LOG_NUM_61,
    UX_MULTIPLE_DEVICE_LOG_NUM_62,
    UX_MULTIPLE_DEVICE_LOG_NUM_63,
    UX_MULTIPLE_DEVICE_LOG_NUM_64,
    UX_MULTIPLE_DEVICE_LOG_NUM_65,
    UX_MULTIPLE_DEVICE_LOG_NUM_66,
    UX_MULTIPLE_DEVICE_LOG_NUM_67,
    UX_MULTIPLE_DEVICE_LOG_NUM_68,
    UX_MULTIPLE_DEVICE_LOG_NUM_69,
    UX_MULTIPLE_DEVICE_LOG_NUM_70,
    UX_MULTIPLE_DEVICE_LOG_NUM_71,
    UX_MULTIPLE_DEVICE_LOG_NUM_72,
    UX_MULTIPLE_DEVICE_LOG_NUM_73,
    UX_MULTIPLE_DEVICE_LOG_NUM_74,
    UX_MULTIPLE_DEVICE_LOG_NUM_75,
    UX_MULTIPLE_DEVICE_LOG_NUM_76,
    UX_MULTIPLE_DEVICE_LOG_NUM_77,
    UX_MULTIPLE_DEVICE_LOG_NUM_78,
    UX_MULTIPLE_DEVICE_LOG_NUM_79,
    UX_MULTIPLE_DEVICE_LOG_NUM_80,
    UX_MULTIPLE_DEVICE_LOG_NUM_81,
    UX_MULTIPLE_DEVICE_LOG_NUM_82,
    UX_MULTIPLE_DEVICE_LOG_NUM_83,
    UX_MULTIPLE_DEVICE_LOG_NUM_84,
    UX_MULTIPLE_DEVICE_LOG_NUM_85,
    UX_MULTIPLE_DEVICE_LOG_NUM_86,
    UX_MULTIPLE_DEVICE_LOG_NUM_87,
    UX_MULTIPLE_DEVICE_LOG_NUM_88,
    UX_MULTIPLE_DEVICE_LOG_NUM_89,
    UX_MULTIPLE_DEVICE_LOG_NUM_90,
    UX_MULTIPLE_DEVICE_LOG_NUM_91,
    UX_MULTIPLE_DEVICE_LOG_NUM_92,
    UX_MULTIPLE_DEVICE_LOG_NUM_93,
    UX_MULTIPLE_DEVICE_LOG_NUM_94,
    UX_MULTIPLE_DEVICE_LOG_NUM_95,
    UX_MULTIPLE_DEVICE_LOG_NUM_96,
    UX_MULTIPLE_DEVICE_LOG_NUM_97,
    UX_MULTIPLE_DEVICE_LOG_NUM_98,
    UX_MULTIPLE_DEVICE_LOG_NUM_99,
    UX_MULTIPLE_DEVICE_LOG_NUM_100,
    UX_MULTIPLE_DEVICE_LOG_NUM_101,
    UX_MULTIPLE_DEVICE_LOG_NUM_102,
    UX_MULTIPLE_DEVICE_LOG_NUM_103,
    UX_MULTIPLE_DEVICE_LOG_NUM_104,
    UX_MULTIPLE_DEVICE_LOG_NUM_105,
    UX_MULTIPLE_DEVICE_LOG_NUM_106,
    UX_MULTIPLE_DEVICE_LOG_NUM_107,
    UX_MULTIPLE_DEVICE_LOG_NUM_108,
    UX_MULTIPLE_DEVICE_LOG_NUM_109,
    UX_MULTIPLE_DEVICE_LOG_NUM_110,
    UX_MULTIPLE_DEVICE_LOG_NUM_111,
    UX_MULTIPLE_DEVICE_LOG_NUM_112,
    UX_MULTIPLE_DEVICE_LOG_NUM_113,
    UX_MULTIPLE_DEVICE_LOG_NUM_114,
    UX_MULTIPLE_DEVICE_LOG_NUM_115,
    UX_MULTIPLE_DEVICE_LOG_NUM_116,
    UX_MULTIPLE_DEVICE_LOG_NUM_117,
    UX_MULTIPLE_DEVICE_LOG_NUM_118,
    UX_MULTIPLE_DEVICE_LOG_NUM_119,
    UX_MULTIPLE_DEVICE_LOG_NUM_120,
    UX_MULTIPLE_DEVICE_LOG_NUM_121,
    UX_MULTIPLE_DEVICE_LOG_NUM_122,
    UX_MULTIPLE_DEVICE_LOG_NUM_123,
    UX_MULTIPLE_DEVICE_LOG_NUM_124,
    UX_MULTIPLE_DEVICE_LOG_NUM_125,
    UX_MULTIPLE_DEVICE_LOG_NUM_126,
    UX_MULTIPLE_DEVICE_LOG_NUM_127,
    UX_MULTIPLE_DEVICE_LOG_NUM_128,
    UX_MULTIPLE_DEVICE_LOG_NUM_129,
    UX_MULTIPLE_DEVICE_LOG_NUM_130,
    UX_MULTIPLE_DEVICE_LOG_NUM_131,
    UX_MULTIPLE_DEVICE_LOG_NUM_132,
    UX_MULTIPLE_DEVICE_LOG_NUM_133,
    UX_MULTIPLE_DEVICE_LOG_NUM_134,
    UX_MULTIPLE_DEVICE_LOG_NUM_135,
    UX_MULTIPLE_DEVICE_LOG_NUM_136,
    UX_MULTIPLE_DEVICE_LOG_NUM_137,
    UX_MULTIPLE_DEVICE_LOG_NUM_138,
    UX_MULTIPLE_DEVICE_LOG_NUM_139,
} ux_multiple_device_log_num_enum;

#endif
