# SignSuite : 签名秘钥类型
    # 0: ECDSA_SHA256
    # 1: ECDSA_SHA256_LEA_CTR
    # 2: ECDSA_SHA256_LEA_CBC_MAC
    # 3: ECDSA_SHA256_AES_CBC

    # 10: SM2_SM3
    # 11: SM2_SM3_LEA_CTR
    # 12: SM2_SM3_LEA_CBC_MAC
    # 13: SM2_SM3_SM4_CBC

    # 30: RSA 4096
    # 34: RSA 4096 - SHA256
[SIGN_CFG]
SignSuite=34
UpgImagePath =
UpgSignedImagePath =
RootKeyFile=
SubKeyFile=

[TOOLS]
UpgToolPath=tools/bin/sign_tool/sign_tool_pltuni
LzmaToolPath=tools/bin/lzma_tool/lzma_tool
UpgToolWinPath=tools\bin\sign_tool\sign_tool_pltuni.exe
LzmaToolWinPath=tools\bin\lzma_tool\lzma_tool.exe

# Default Single sign mode. If there are 'RootKeyFileExt' & 'SubKeyFileExt' then means Double Sign mode.
# RootKeyFileExt=
# SubKeyFileExt=
[FOTA_KEY_AREA]
ImageId=0xCB8D154E
StructVersion=0x00010000
KeyOwnerId=0
KeyId=0
# KeyAlg :Algorithm of the external public key:
    # 0x2A13C812: ECC256_SHA256
    # 0x2A13C823: SM2_SM3
    # 0x2A13C845: RSA4096_SHA256
    # 0x2A13C856: SHA256(ECC256 FORMAT)
    # 0x2A13C867: SHA256(RSA4096)
    # 0x2A13C878: SM3_ONLY
    # Others: Invalid
KeyAlg=0x2A13C867
KeyVersion=0x00000000
KeyVersionMask=0x00000000
Msid=0x00000000
MsidMask=0x00000000

[FOTA_INFO_AREA]
ImageId=0xCB8D154E
StructVersion=0x00010000
Version=0x00000000
VersionMask=0x00000000
Msid=0x00000000
MsidMask=0x00000000
HardwareID=0

[flashboot]
HeaderMagic=0x464F5451
ImageId=0x4B1E3C2D
# DecompressFlag :升级镜像模式标记
    # 0x3C7896E1 - compressed image
    # 0x44494646 - diff image
    # other :原始镜像不处理
DecompressFlag=0
# ReRncFlag:升级镜像加密标记
    # 0x3C7896E1:加密
    # other:不加密
ReRncFlag=0x0
# RootKeyType : 用于加密图像的根密钥，在ReEncFlag启用时有效
    # 0 - ODRK0, for TEE runtime FW image.
    # 1 - ODRK1, for Protocol and REE Application image.
RootKeyType=0x0
# 用于解密更新映像的1级加密保护密钥，在ReEncFlag启用时有效
ProtectionKeyL1=
# 用于解密更新映像的2级加密保护密钥，在ReEncFlag启用时有效
ProtectionKeyL2=
version_ext=0x00000000
version_mask=0x00000000

[nv]
HeaderMagic=0x464F5451
ImageId=0xCB9E063C
# DecompressFlag :升级镜像模式标记
    # 0 :原始镜像不处理 (nv只能使用该值)
DecompressFlag=0
# ReRncFlag:升级镜像加密标记
    # 0x3C7896E1:加密
    # other:不加密
ReRncFlag=0x0
# RootKeyType : 用于加密图像的根密钥，在ReEncFlag启用时有效
    # 0 - ODRK0, for TEE runtime FW image.
    # 1 - ODRK1, for Protocol and REE Application image.
RootKeyType=0x0
# 用于解密更新映像的1级加密保护密钥，在ReEncFlag启用时有效
ProtectionKeyL1=
# 用于解密更新映像的2级加密保护密钥，在ReEncFlag启用时有效
ProtectionKeyL2=
version_ext=0x00000000
version_mask=0x00000000

[application]
HeaderMagic=0x464F5451
ImageId=0x4B0F2D2D
# DecompressFlag :升级镜像模式标记
    # 0x3C7896E1 - compressed image
    # 0x44494646 - diff image
    # other :原始镜像不处理
DecompressFlag=0x3C7896E1
# ReRncFlag:升级镜像加密标记
    # 0x3C7896E1:加密
    # other:不加密
ReRncFlag=0x0
# RootKeyType : 用于加密图像的根密钥，在ReEncFlag启用时有效
    # 0 - ODRK0, for TEE runtime FW image.
    # 1 - ODRK1, for Protocol and REE Application image.
RootKeyType=0x0
# 用于解密更新映像的1级加密保护密钥，在ReEncFlag启用时有效
ProtectionKeyL1=
# 用于解密更新映像的2级加密保护密钥，在ReEncFlag启用时有效
ProtectionKeyL2=
version_ext=0x00000000
version_mask=0x00000000
