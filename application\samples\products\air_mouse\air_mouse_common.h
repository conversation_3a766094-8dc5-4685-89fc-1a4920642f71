/**
 * Copyright (c) @CompanyNameMagicTag 2024-2024. All rights reserved. \n
 * 2024-03-22， Create file. \n
 */
#ifndef AIR_MOUSE_COMMON_H
#define AIR_MOUSE_COMMON_H

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

#define SLEEP_COUNT_THRESHOLD               500 // 500次相同报点进入睡眠

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif