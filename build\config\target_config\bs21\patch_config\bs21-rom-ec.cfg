[Patch Info]
Device_Code_Version = Version1

Patch_Cpu_Core = APPLICATION

Patch_File_Address  = 0x00000000
Patch_TBL_Address   = 0x00000300
Patch_TBL_Run_Address  = 0x00040000
Table_Max_Size         = 4
Table_Reg_Size         = 4
TABLE_REG_CONUT        = 128

[ROM Info]
ROM_Address        = 0x00015c00
ROM_Size           = 0x0002a400

[Output Info]
CMP_Bin_File        = cmp.bin
TBL_Bin_File        = tbl.bin
RW_Bin_File         = patch.bin

[Function]
####start platform patch
####end   platform patch

####start btc patch
evt_task_ble_acl_event_int_tx_ack evt_task_ble_acl_event_int_tx_ack_patch
evt_task_ble_acl_cancel_cbk evt_task_ble_acl_cancel_cbk_patch
evt_task_ble_acl_check_latency evt_task_ble_acl_check_latency_patch
evt_task_ble_acl_hop_alg1_latency_compensation evt_task_ble_acl_hop_alg1_latency_compensation_patch
evt_task_ble_acl_instant_judge evt_task_ble_acl_instant_judge_patch
evt_task_ble_acl_save_sync_time_info evt_task_ble_acl_save_sync_time_info_patch
evt_task_ble_acl_check_time_out evt_task_ble_acl_check_time_out_patch
bgtp_sleep_wait_to_idle bgtp_sleep_wait_to_idle_patch
dpc_fsm_msg_process dpc_fsm_msg_process_patch
dpc_call_func dpc_call_func_patch
evt_task_ble_adv_get_stop_status evt_task_ble_adv_get_stop_status_patch
es_process_cancel_cbk es_process_cancel_cbk_patch
evt_prog_finish_eeq_isr evt_prog_finish_eeq_isr_patch
lm_ble_send_terminate_process lm_ble_send_terminate_process_patch
evt_task_ble_acl_set_interval evt_task_ble_acl_set_interval_patch
ble_isr_get_cur_isr_queue_head ble_isr_get_cur_isr_queue_head_patch
gle_isr_get_cur_isr_queue_head gle_isr_get_cur_isr_queue_head_patch
dts_malloc dts_malloc_patch
####end btc patch

####start bth patch
bth_init bth_init_patch
bt_init_stack bt_init_stack_patch
smp_require_end smp_require_end_patch
####end bth patch
