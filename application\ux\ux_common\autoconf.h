/*
 *
 * Automatically generated file; DO NOT EDIT.
 * Audio Configuration
 *
 */
#ifndef _AUTOCONF_H
#define _AUTOCONF_H

#define CONFIG_ADD_PRODUCT_ID_FIELD 1
#define CONFIG_NEARBY_RSSI (-57)
#define CONFIG_FIRMWARE_PATCH_VERSION "0"
#define CONFIG_DEVICE_RESTART_TIMEOUT 60
#define CONFIG_DEFAULT_DEVICETYPE 0
#define CONFIG_FOTA_VERSION_ALL "all"
#define CONFIG_AUDIO_CODEC_CC0400 1
#define CONFIG_OTA_PAYLOAD_SIZE 512
#define CONFIG_LOCAL_NAME "melody"
#define CONFIG_AUDIO_FEATURE_ASR 1
#define CONFIG_PRODUCT_NAME "melody"
#define CONFIG_FIRMWARE_HARDWARE_VERSION_LEFT "HL1SAKM_Ver.A"
#define CONFIG_FIRMWARE_OTA_NAME_HW "BTFCMSHK00"
#define CONFIG_SECURITY_SUB_VERSION "97"
#define CONFIG_AUDIO_CODEC_DMIC 1
#define CONFIG_DSP_SUB_VERSION "97"
#define CONFIG_SPECIAL_SOFTWARE_VERSION_FORBID_OTA "1.X.X.X"
#define CONFIG_AUDIO_CODEC 1
#define CONFIG_DEFAULT_LEFT_DOUBLE_CLICK 3
#define CONFIG_AUDIO_CODEC_VAD 1
#define CONFIG_M1_PRESS_ALG 1
#define CONFIG_VOLUME_STEP 2
#define CONFIG_AUDIO_EFFECT_EQ 1
#define CONFIG_MODEL_NAME_HONOR "CM-SHK10"
#define CONFIG_DEFAULT_SUBMODELID 0
#define CONFIG_UX_CLOSE_BOX_REBOOT_FEATURE 1
#define CONFIG_CIRCLE_RSSI (-85)
#define CONFIG_HILINK_PRODID "004O"
#define CONFIG_FIRMWARE_OTA_NAME_HONOR "BTFCMSHK10"
#define CONFIG_AUDIO_FEATURE_HIRES_RECORD 1
#define CONFIG_FLASH_DEVICE_INNER 1
#define CONFIG_AUDIO_FEATURE_A2DP 1
#define CONFIG_AUDIO_FEATURE_AH 1
#define CONFIG_AUDIO_CODEC_VPU 1
#define CONFIG_SUB_VERSION "20"
#define CONFIG_UX_HD_SCO 1
#define CONFIG_AUDIO_CODEC_MCLKO_12_288M 1
#define CONFIG_NV_FCC_IND_ERROR 0
#define CONFIG_SYSTEM_MU0101 1
#define CONFIG_FULL_RESTART_BASE_RATE 49152
#define CONFIG_GESTURE_ORIGIN_ALG 1
#define CONFIG_FIRMWARE_MODELNAME_HONOR "BTFCMSHK10-000026"
#define CONFIG_EXTU_VERSION "melody"
#define CONFIG_DEFAULT_MODELID 39
#define CONFIG_BOX_OTA_PAYLOAD_SIZE 512
#define CONFIG_WEAR_ORIGIN_ALG 1
#define CONFIG_ROMFS 0
#define CONFIG_PRO_TWS 1
#define CONFIG_DEFAULT_WORKMODE 1
#define CONFIG_END_USER_SUB_VERSION "20"
#define CONFIG_FIRMWARE_SPECIAL_SOFTWARE_VERSION "0"
#define CONFIG_DEFAULT_RIGHT_DOUBLE_CLICK 4
#define CONFIG_FOTA_HEADSET_BATTERY_LIMIT 30
#define CONFIG_WEAR_APP 1
#define CONFIG_LPC_SIMPLIFY_STATE 1
#define CONFIG_POWER_APP_WATCHDOG 1
#define CONFIG_FIRMWARE_MODELNAME_HW "BTFCMSHK00-000027"
#define CONFIG_AUDIO_SIMULATION_CC0800 1
#define CONFIG_AUDIO_FEATURE_NOTIFICATION_SHARE_MEMORY 1
#define CONFIG_AUDIO_FEATURE_SYSSOUND 1
#define CONFIG_HILINK_VERSION "1.0.0"
#define CONFIG_POWER_APP_MASSDATA 1
#define CONFIG_FIRMWARE_HARDWARE_VERSION_RIGHT "HL1SAKM2_Ver.A"
#define CONFIG_BT_NAME_VERSION "melody"
#define CONFIG_DUAL_ANTENNA 1
#define CONFIG_AUDIO_FEATURE_ANC 1
#define CONFIG_AUDIO_EFFECT_CSTM_EQ 1
#define CONFIG_UX_FEATURE_SOUND_TRIGGER 1
#define CONFIG_MODEL_ID_DEFAULT 305
#define CONFIG_AUDIO_FEATURE_SCO 1
#define CONFIG_ELNA_ENABLE 1
#define CONFIG_DEFAULT_MARKET 0
#define CONFIG_BOX_MCU_TYPE_DEFAULT_ID 0x1001
#define CONFIG_DEFAULT_ADVERSION 0
#define CONFIG_DEFAULT_EARSIDE 2
#define CONFIG_MODEL_NAME_HW "CM-SHK00"
#define CONFIG_POWER_APP 1
#define CONFIG_AUDIO_EFFECT_HISTEN 1
#define CONFIG_MELODY_CHIPSET 1
#define CONFIG_EMPU 1
#define CONFIG_ELNA_SWITCH 1
#define CONFIG_VERSION_END_USER 1
#endif
