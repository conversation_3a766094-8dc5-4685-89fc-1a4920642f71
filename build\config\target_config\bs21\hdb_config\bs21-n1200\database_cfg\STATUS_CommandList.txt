//格式说明：
//[]内的内容为处理的消息ID和参数显示设置，其中MsgID值为16进制，Parameter Number值为10进制，后面的显示为按格式显示，其中除开格式描述字符的其他字符串按字符串显示，格式显示中各个参数显示间以“；”间隔，最后一个参数显示后不加“；”
//格式描述字符说明：%d为将接收到的该参数数据按10进制显示，%x为将接收到的该参数数据按16进制显示，%s为将接收到的该参数数据在对应的参数表中查询对应的字符串再显示该字符串
//所有的要显示参数都必须有对应的Start、End标记，以%d、%x格式显示的参数仅需加上这两个标记即可，以%s格式显示的参数需添加该标记以及填充对应关系，若无该对应关系，则显示时会显示找不到该参数数据对应的字符串
//对应关系表：以“ParameterX_CommandList Start:”作为开始标记，以“ParameterX_CommandList End”作为结束标记，每行都有两栏，以TAB键分隔，其中第一栏为索引，第二栏为对应字符串，索引为16进制数据
//对于数量多且用于多处的对应关系表，添加了提取这个公共部分的功能，公共关系表请放在注释//Public Content START和注释//Public Content END之间部分。
//公共关系表添加方法，首先添加公共关系表标头，格式为{标头名 START}，然后从第二行开始添加对应表，和普通对应表格式相同，公共对应表结束后，要在最后一行添加{PUBLIC END}标尾示意结束。
//公共关系表使用方法，在需要使用的公共关系表的参数对应表位置，添加格式为$$标头名。
//以“//”开头的为注释行
//
//注意：Status MsgID分配【BT：0x0000……0x2fff】【GNSS：0x3000……0x5fff】【NFC：0x6000……0x8fff】【FM：0x9000……0x9fff】，其他预留~
//
//示例说明:
//{BT OTA}			定义公共对应表
//0x1	Show_1
//0x2	Show_2
//[MsgID=0x0001;Parameter Num=3;Parameter1 Show=%s;Parameter2 Show=%x;Parameter3 Show=%s]
//Parameter1_CommandList Start:
//$$BT OTA			使用公共对应表
//Parameter1_CommandList End
//Parameter2_CommandList Start:
//Parameter2_CommandList End
//Parameter3_CommandList Start:
//Index	Show
//0x1	Show_1
//0x2	Show_2
//0x3	Show_3
//0xa	Show_a
//Parameter3_CommandList End
//如果传递过来的参数1、2、3均为0xa，则SDT上显示部分为:Parameter1 Show=10;Parameter2 Show=0xa;Parameter3 Show=Show_a
//
//BT
//[MsgID=0x0801;Parameter Number=4;Src=%s;Dest=%s;Link Id=%d;Msg:%d]
//Parameter1_CommandList Start:
//Index	Show
//0	TASK_LM
//Parameter1_CommandList End
//
//Parameter2_CommandList Start:
//Index	Show
//1	TASK_LC
//Parameter2_CommandList End
//
//Parameter3_CommandList Start:
//Parameter3_CommandList End
//Parameter4_CommandList Start:
//Parameter4_CommandList End
//




//Public Content START		公共对应表添加在Public Content START与Public Content END之间

{BT CFILE START}
0x0000	hci_romcb.c
0x0001	fsm.c
0x0002	bt_main.c
0x0003	bt_thread.c
0x0004	hci_if.c
0x0005	evt_task_fsm.c
0x0006	dm_ble_fal.c
0x0007	dm_ble_rpa.c
0x0008	lm_ble_adv.c
0x0009	lm_ble_adv_ext_act.c
0x000a	lm_ble_adv_fsm.c
0x000b	lm_ble_adv_legacy_act.c
0x000c	lm_ble_acl.c
0x000d	lm_ble_acl_per_fsm.c
0x000e	lm_ble_acl_srv_ver.c
0x000f	lm_ble_acl_srv_feat.c
0x0010	lm_ble_acl_srv_enc.c
0x0011	lm_ble_acl_srv_enc_central.c
0x0012	lm_ble_acl_srv_enc_peripheral.c
0x0013	lm_ble_acl_srv_conn_update.c
0x0014	lm_ble_acl_srv_phy_upd.c
0x0015	lm_ble_acl_srv_uhd_upd.c
0x0016	lm_ble_acl_srv_map.c
0x0017	lm_ble_acl_srv_pwr_ctrl.c
0x0018	lm_ble_acl_srv_data_length.c
0x0019	lm_ble_acl_srv_cs.c
0x001a	lm_ble_acl_disc.c
0x001b	lm_ble_scan.c
0x001c	lm_ble_scan_fsm.c
0x001d	lm_ble_legacy_scan_act.c
0x001e	lm_ble_ext_scan_act.c
0x001f	lm_ble_initiate.c
0x0020	lm_ble_legacy_initiate_act.c
0x0021	lm_ble_initiate_fsm.c
0x0022	lm_ble_ext_initiate_act.c
0x0023	lm_ble_test_mode_act.c
0x0024	lm_ble_acl_fsm.c
0x0025	lm_ble_test_mode.c
0x0026	lm_ble_test_mode_fsm.c
0x0027	lm_gle_acb_disc.c
0x0028	lm_gle_acb_fsm.c
0x0029	lm_gle_acb.c
0x002a	lm_gle_iob_disc.c
0x002b	lm_gle_iob_fsm.c
0x002c	lm_gle_iob.c
0x002d	evt_task_ble_acl.c
0x002e	evt_task_ble_adv.c
0x002f	evt_task_ble_scan.c
0x0030	evt_task_ble_scan_isr.c
0x0031	evt_task_ble_initiate.c
0x0032	evt_task_ble_initiate_isr.c
0x0033	evt_task_chnl_scan.c
0x0034	evt_task_ble_test_mode.c
0x0035	evt_task_ble_test_mode_isr.c
0x0036	evt_task_ble_cs.c
0x0037	evt_task_gle_acb.c
0x0038	evt_sched.c
0x0039	evt_sched_calc.c
0x003a	evt_sched_mgr.c
0x003b	evt_sched_util.c
0x003c	evt_prog.c
0x003d	ble_isr.c
0x003e	gle_isr.c
0x003f	hci_bt.c
0x0040	hci_gle.c
0x0041	em_sed.c
0x0042	hw_em.c
0x0043	dts_mem.c
0x0044	dts_hci.c
0x0045	em_tx_buff.c
0x0046	em_tx_desc.c
0x0047	em_mgr.c
0x0048	bt_adpll.c
0x0049	bt_rf_table.c
0x004a	bt_hal_control.c
0x004b	mpw_poweron.c
0x004c	btc_timer.c
0x004d	bgtp_sleep_sw.c
0x004e	bgtp_pmu_fsm.c
0x004f	btdfx_fn.c
0x0050	lm_chnl_scan.c
0x0051	dts_coex.c
0x0052	coex_adapter.c
0x0053	coex_if.c
0x0054	encrypt.c
0x0055	lm_ble_acl_srv_priv_feat.c
0x0056	bt_cali_func.c
0x0057	bt_cali_main.c
0x0058	bt_hal_cali.c
0x0059	em_rx_desc.c
0x005a	cali_entrance.c
0x005b	cali_state.c
0x005c	cali_rx.c
0x005d	cali_tx.c
0x005e	cali_adpll.c
0x005f	cali_list.c
0x0060	hal_bt_cali.c
0x0061	rf_calibration_alg.c
0x0062	rf_accumulation.c
0x0063	lm_gle_scan_fsm.c
0x0064	lm_gle_scan_act.c
0x0065	evt_task_gle_scan.c
0x0066	lm_gle_scan.c
0x0067	lm_gle_initiate_fsm.c
0x0068	evt_task_gle_initiate.c
0x0069	lm_gle_initiate.c
0x006a	lm_gle_initiate_act.c
0x006b	lm_ble_per_adv.c
0x006c	lm_ble_per_adv_fsm.c
0x006d	lm_ble_per_adv_act.c
0x006e	evt_task_ble_per_adv.c
0x006f	lm_ble_per_sync.c
0x0070	lm_ble_per_sync_fsm.c
0x0071	lm_ble_per_sync_act.c
0x0072	evt_task_ble_per_sync.c
0x0073	evt_task_ble_adv_isr.c
0x0074	evt_task_gle_scan_isr.c
0x0075	evt_task_gle_initiate_isr.c
0x0076	evt_task_gle_adv.c
0x0077	ecc_p256.c
0x0078	evt_task_gle_test_mode.c
0x0079	evt_task_gle_test_mode_isr.c
0x007a	lm_gle_test_mode_act.c
0x007b	lm_gle_test_mode.c
0x007c	lm_gle_test_mode_fsm.c
0x007d	dts_mips.c
0x007e	evt_task_gle_iob.c
0x007f	lm_gle_adv_act.c
0x0080	evt_task_gle_adv_isr.c
0x0081	lm_gle_acb_srv_conn_update.c
0x0082	lm_gle_acb_srv_feat.c
0x0083	lm_gle_acb_srv_phy_update.c
0x0084	evt_task_gle_adv_scan_comm.c
0x0085	lm_gle_acb_srv_map.c
0x0086	lm_gle_acb_srv_ver.c
0x0087	enc_ke_ecc.c
0x0088	lm_gle_acb_srv_enc.c
0x0089	evt_task_ble_ext_adv.c
0x008a	lm_ble_scan_filter.c
0x008b	lm_ble_scan_act_ram.c
0x008c	lm_ble_ext_scan_act_ram.c
0x008d	lm_gle_acb_srv_data_length.c
0x008e	evt_task_gle_imb.c
0x008f	lm_gle_acb_srv_imb.c
0x0090	hci_comm_if.c
0x0091	lm_gle_acb_srv_pwr_ctrl.c
0x0092	dm_co.c
0x0093	lm_ble_acl_srv_conn_update_fsm_act.c
0x0094	lm_gle_imb.c
0x0095	enc_kd_sm3.c
0x0096	evt_task_comm.c
0x0097	lm_ble_comm.c
0x0098	lm_gle_acb_srv_set_evt_param.c
0x0099	evt_task_gle_acb_isr.c
0x009a	bt_customize.c
0x009b	cali_dll.c
0x009c	evt_task_gle_acb_retry.c
0x009d	evt_task_gle_comm.c
0x009e	lm_gle_acb_srv_set_low_latency.c
0x009f	evt_task_ble_ext_adv_ram.c
0x00a0	evt_task_ble_acl_ram.c
0x00a1	evt_task_ble_scan_ram.c
0x00a2	evt_task_ble_initiate_ram.c
0x00a3	evt_prog_ram.c
0x00a4	lm_ble_acl_ram.c
0x00a5	evt_sched_mgr_ram.c
0x00a6	evt_sched_ram.c
0x00a7	evt_sched_util_ram.c
0x00a8	patch_lm_ble_adv.c
0x00a9	evt_task_gle_acb_low_latency.c
0x00aa	patch_evt_task_ble_acl.c
0x00ab	lm_gle_acb_srv_map_ram.c
0x00ac	evt_task_ble_adv_ram.c
{PUBLIC END}

{BT FSM_TYPE START}
0x0000	FSM_INVALID
0x0001	FSM_TYPE_HCI_BT
0x0002	FSM_TYPE_HCI_GLE
0x0003	FSM_TYPE_HCI_BT_SUB
0x0004	FSM_TYPE_HCI_GLE_SUB
0x0005	FSM_TYPE_DM_COMM
0x0006	FSM_TYPE_DM_BLE
0x0007	FSM_TYPE_DM_GLE
0x0008	FSM_TYPE_DM_BT
0x0009	FSM_TYPE_LM_BLE_ADV
0x000a	FSM_TYPE_LM_BLE_EXT_ADV_INIT
0x000b	FSM_TYPE_LM_BLE_EXT_ADV
0x000c	FSM_TYPE_LM_BLE_EXT_ADV_COMM
0x000d	FSM_TYPE_LM_BLE_PER_ADV
0x000e	FSM_TYPE_LM_BLE_SCAN
0x000f	FSM_TYPE_LM_BLE_PER_SYNC
0x0010	FSM_TYPE_LM_BLE_PER_SYNC_MGR
0x0011	FSM_TYPE_LM_BLE_INITIATE
0x0012	FSM_TYPE_LM_BLE_ACL
0x0013	FSM_TYPE_LM_BLE_FEATS
0x0014	FSM_TYPE_LM_BLE_VER
0x0015	FSM_TYPE_LM_BLE_CONN_UPD
0x0016	FSM_TYPE_LM_BLE_ENABLE_ENC
0x0017	FSM_TYPE_LM_BLE_PHY_UPD
0x0018	FSM_TYPE_LM_BLE_UHD_UPD
0x0019	FSM_TYPE_LM_BLE_MAP_UPD
0x001a	FSM_TYPE_LM_BLE_TEST_MODE
0x001b	FSM_TYPE_LM_BLE_PWR_CTRL
0x001c	FSM_TYPE_LM_BLE_DATA_LEN_UPD
0x001d	FSM_TYPE_LM_BLE_PRIV_FEAT
0x001e	FSM_TYPE_LM_GLE
0x001f	FSM_TYPE_LM_GLE_ACB
0x0020	FSM_TYPE_LM_GLE_IOB
0x0021	FSM_TYPE_LM_GLE_TEST_MODE
0x0022	FSM_TYPE_LM_BT
0x0023	FSM_TYPE_LM_BLE_CS
0x0024	FSM_TYPE_EVT_TASK_BLE_ACL
0x0025	FSM_TYPE_EVT_TASK_BLE_ADV
0x0026	FSM_TYPE_EVT_TASK_BLE_PRIM_ADV
0x0027	FSM_TYPE_EVT_TASK_BLE_AUX_ADV
0x0028	FSM_TYPE_EVT_TASK_BLE_PER_ADV
0x0029	FSM_TYPE_EVT_TASK_BLE_PER_SYNC
0x002a	FSM_TYPE_EVT_TASK_BLE_SCAN
0x002b	FSM_TYPE_EVT_TASK_BLE_INITIATE
0x002c	FSM_TYPE_EVT_TASK_GLE
0x002d	FSM_TYPE_EVT_TASK_GLE_ACB
0x002e	FSM_TYPE_EVT_TASK_GLE_IOB
0x002f	FSM_TYPE_EVT_TASK_ISO
0x0030	FSM_TYPE_EVT_TASK_BT
0x0031	FSM_TYPE_EVT_TASK_CHNL_SCAN
0x0032	FSM_TYPE_EVT_TASK_BLE_TEST_MODE
0x0033	FSM_TYPE_EVT_TASK_BLE_CS
0x0034	FSM_TYPE_SCH
0x0035	FSM_TYPE_PROG
0x0036	FSM_TYPE_DPC
0x0037	FSM_TYPE_ISR
0x0038	FSM_TYPE_TIMER
0x0039	FSM_TYPE_ENCRYPT
0x003a	FSM_TYPE_COEX
0x003b	FSM_TYPE_LM_GLE_SCAN
0x003c	FSM_TYPE_LM_GLE_INITIATE
0x003d	FSM_TYPE_EVT_TASK_GLE_SCAN
0x003e	FSM_TYPE_EVT_TASK_GLE_SCAN_AUX
0x003f	FSM_TYPE_EVT_TASK_GLE_SCAN_REQ
0x0040	FSM_TYPE_EVT_TASK_GLE_INITIATE
0x0041	FSM_TYPE_EVT_TASK_GLE_INITIATE_AUX
0x0042	FSM_TYPE_EVT_TASK_GLE_INITIATE_REQ
0x0043	FSM_TYPE_LM_GLE_ADV
0x0044	FSM_TYPE_LM_GLE_ADV_COMM
0x0045	FSM_TYPE_EVT_TASK_GLE_ADV
0x0046	FSM_TYPE_EVT_TASK_GLE_TEST_MODE
0x0047	FSM_TYPE_LOWPOWER
0x0048	FSM_TYPE_EVT_TASK_GLE_SCAN_RSP
0x0049	FSM_TYPE_LM_GLE_CONN_UPD
0x004a	FSM_TYPE_LM_GLE_PHY_UPD
0x004b	FSM_TYPE_LM_GLE_MAP_UPD
0x004c	FSM_TYPE_LM_GLE_FEATS
0x004d	FSM_TYPE_LM_GLE_IMB
0x004e	FSM_TYPE_LM_GLE_IMG
0x004f	FSM_TYPE_LM_GLE_IMG_FSM_EXISTED
0x0050	FSM_TYPE_LM_GLE_ENC_CHANGE
0x0051	FSM_TYPE_LM_GLE_VER
0x0052	FSM_TYPE_LM_GLE_DATA_LEN_UPD
0x0053	FSM_TYPE_EVT_TASK_GLE_IMB
0x0054	FSM_TYPE_DISPATCH_HCI_GLE_DISCONNECT
0x0055	FSM_TYPE_LM_GLE_PWR_CTRL
0x0056	FSM_TYPE_LM_GLE_ACB_EVT_PARAM_UPDATE
0x0057	FSM_TYPE_EVT_TASK_GLE_ACB_RETRY
0x0058	FSM_TYPE_LM_GLE_ACB_LOW_LATENCY_UPDATE
0x0059	FSM_TYPE_EVT_TASK_GLE_AUX_ADV
0x005a	FSM_TYPE_EVT_TASK_GLE_REQ_RX
0x005b	FSM_TYPE_EVT_TASK_GLE_RSP_ADV
0x005c	FSM_TYPE_EVT_TASK_GLE_INITIATE_RSP
0x005d	FSM_TYPE_BUTT
{PUBLIC END}

{BT FSM_STATE START}
0x0000	LM_BLE_STATE_ADV_STANDBY
0x0001	LM_BLE_STATE_ADV_ADVERTISING
0x0002	LM_BLE_STATE_ADV_STOPPING
0x0003	LM_BLE_STATE_ADV_REMOVING
0x0004	LM_BLE_STATE_ADV_COMM_IDLE
0x0005	LM_BLE_STATE_ADV_COMM_BUSY
0x0006	LM_BLE_STATE_PER_ADV_IDLE
0x0007	LM_BLE_STATE_PER_ADV_CONFIGING
0x0008	LM_BLE_STATE_PER_ADV_PENDING
0x0009	LM_BLE_STATE_PER_ADV_RUNNING
0x000a	LM_BLE_STATE_PER_ADV_RUNNING_CTE_RUNNING
0x000b	LM_BLE_STATE_PER_SYNC_MGR_STANDBY
0x000c	LM_BLE_STATE_PER_SYNC_MGR_SYNCING
0x000d	LM_BLE_STATE_PER_SYNC_STANDBY
0x000e	LM_BLE_STATE_PER_SYNC_PENDING
0x000f	LM_BLE_STATE_PER_SYNC_SYNCING
0x0010	LM_BLE_STATE_PER_SYNC_CANCELLING
0x0011	LM_BLE_STATE_PER_SYNC_SYNCED
0x0012	LM_BLE_STATE_SCAN_OFF
0x0013	LM_BLE_STATE_SCANNING
0x0014	LM_BLE_STATE_SCAN_STOPPING
0x0015	LM_BLE_STATE_INITIATE_OFF
0x0016	LM_BLE_STATE_INITIATING
0x0017	LM_BLE_STATE_INITIATE_STOPPING
0x0018	LM_BLE_STATE_CONNECTION
0x0019	LM_BLE_STATE_ACL_WAIT_DISC_TX_ACK
0x001a	LM_BLE_STATE_ACL_WAIT_SCHED_STOP
0x001b	LM_BLE_STATE_FEATS_IDLE
0x001c	LM_BLE_STATE_WAIT_FEATS_RSP
0x001d	LM_BLE_STATE_VER_IDLE
0x001e	LM_BLE_STATE_WAIT_VER_RSP
0x001f	LM_BLE_STATE_MAP_UPDATA_IDLE
0x0020	LM_BLE_STATE_WAIT_MAP_IND_ACK
0x0021	LM_BLE_STATE_WAIT_MAP_IND_ACK_INSTANT_PAST
0x0022	LM_BLE_STATE_WAIT_MAP_INSTANT
0x0023	LM_BLE_STATE_PHY_UPD_IDLE
0x0024	LM_BLE_STATE_PHY_UPD_WAIT_RSP
0x0025	LM_BLE_STATE_PHY_UPD_WAIT_IND
0x0026	LM_BLE_STATE_PHY_UPD_WAIT_INST
0x0027	LM_BLE_STATE_UHD_UPD_IDLE
0x0028	LM_BLE_STATE_UHD_UPD_WAIT_RSP
0x0029	LM_BLE_STATE_UHD_UPD_WAIT_IND
0x002a	LM_BLE_STATE_UHD_UPD_WAIT_INST
0x002b	LM_BLE_STATE_CONN_UPDATE_IDLE
0x002c	LM_BLE_STATE_WAIT_UPDATE_RSP
0x002d	LM_BLE_STATE_WAIT_UPDATE_IND
0x002e	LM_BLE_STATE_WAIT_UPDATE_CMP
0x002f	LM_BLE_STATE_WAIT_UPDATE_HOST_REPLY
0x0030	LM_BLE_STATE_CENTRAL_ENCRYPTED
0x0031	LM_BLE_STATE_CENTRAL_STANDBY
0x0032	LM_BLE_STATE_CENTRAL_REQUEST_ENC_PAUSE
0x0033	LM_BLE_STATE_CENTRAL_GENERATE_SKD_IV
0x0034	LM_BLE_STATE_CENTRAL_REQUEST_ENC
0x0035	LM_BLE_STATE_CENTRAL_CALCULATE_SK
0x0036	LM_BLE_STATE_CENTRAL_SK_READY
0x0037	LM_BLE_STATE_CENTRAL_ENC_START_REQUESTED
0x0038	LM_BLE_STATE_CENTRAL_RESPOND_ENC_START
0x0039	LM_BLE_STATE_PERIPHERAL_ENCRYPTED
0x003a	LM_BLE_STATE_PERIPHERAL_RESPOND_ENC_PAUSE
0x003b	LM_BLE_STATE_PERIPHERAL_STANDBY
0x003c	LM_BLE_STATE_PERIPHERAL_RESPOND_ENC
0x003d	LM_BLE_STATE_PERIPHERAL_GENERATE_SKD_IV
0x003e	LM_BLE_STATE_PERIPHERAL_REQUEST_LTK
0x003f	LM_BLE_STATE_PERIPHERAL_CALCULATE_SK
0x0040	LM_BLE_STATE_PERIPHERAL_REQUEST_ENC_START
0x0041	LM_BLE_STATE_PWR_CTRL_IDLE
0x0042	LM_BLE_STATE_WAIT_PWR_CTRL_RSP
0x0043	LM_BLE_STATE_TEST_MODE_IDLE
0x0044	LM_BLE_STATE_TEST_MODE_BUSY
0x0045	LM_BLE_STATE_TEST_MODE_STOPPING
0x0046	LM_BLE_STATE_DATA_LENGTH_IDLE
0x0047	LM_BLE_STATE_DATA_LENGTH_WAIT_RSP
0x0048	LM_BLE_STATE_PRIV_FEAT_IDLE
0x0049	LM_BLE_STATE_WAIT_PRIV_FEAT_RSP
0x004a	LM_GLE_STATE_CONNECTION
0x004b	LM_GLE_STATE_ACB_WAIT_DISC_TX_ACK
0x004c	LM_GLE_STATE_ACB_WAIT_SCHED_STOP
0x004d	LM_GLE_STATE_IOB_WAIT_DISC_TX_ACK
0x004e	LM_GLE_STATE_IOB_WAIT_SCHED_STOP
0x004f	LM_BLE_STATE_CS_IDLE
0x0050	LM_BLE_STATE_WAIT_REMOTE_CAP_RSP
0x0051	LM_BLE_STATE_WAIT_REMOTE_CONFIG_RSP
0x0052	LM_BLE_STATE_WAIT_REMOTE_CS_RSP
0x0053	LM_BLE_STATE_WAIT_REMOTE_CS_IND
0x0054	EVT_TASK_STATE_IDLE
0x0055	EVT_TASK_STATE_ENDING
0x0056	LM_GLE_STATE_SCAN_OFF
0x0057	LM_GLE_STATE_SCANNING
0x0058	LM_GLE_STATE_SCAN_STOPPING
0x0059	LM_GLE_STATE_INITIATE_OFF
0x005a	LM_GLE_STATE_INITIATING
0x005b	LM_GLE_STATE_INITIATE_STOPPING
0x005c	LM_GLE_STATE_ADV_STANDBY
0x005d	LM_GLE_STATE_ADV_ADVERTISING
0x005e	LM_GLE_STATE_ADV_STOPPING
0x005f	LM_GLE_STATE_ADV_REMOVING
0x0060	LM_GLE_STATE_ADV_COMM_IDLE
0x0061	LM_GLE_STATE_ADV_COMM_BUSY
0x0062	LM_GLE_STATE_TEST_MODE_IDLE
0x0063	LM_GLE_STATE_TEST_MODE_BUSY
0x0064	LM_GLE_STATE_TEST_MODE_STOPPING
0x0065	LM_GLE_STATE_CONN_UPDATE_IDLE
0x0066	LM_GLE_STATE_CONN_UPDATE_WAIT_RSP
0x0067	LM_GLE_STATE_CONN_UPDATE_WAIT_IND
0x0068	LM_GLE_STATE_CONN_UPDATE_WAIT_CMP
0x0069	LM_GLE_STATE_CONN_UPDATE_WAIT_HOST_REPLY
0x006a	LM_GLE_STATE_ENCRYPTED
0x006b	LM_GLE_STATE_ENC_STANDBY
0x006c	LM_GLE_STATE_G_WAIT_ENC_STOP_RSP
0x006d	LM_GLE_STATE_G_WAIT_ENC_STOP_RSP_ACK
0x006e	LM_GLE_STATE_G_GENERATE_SKD_IV
0x006f	LM_GLE_STATE_G_WAIT_ENC_RSP
0x0070	LM_GLE_STATE_G_CALCULATE_SK
0x0071	LM_GLE_STATE_G_SK_READY
0x0072	LM_GLE_STATE_G_ENC_START_REQUESTED
0x0073	LM_GLE_STATE_G_WAIT_ENC_START_RSP
0x0074	LM_GLE_STATE_T_WAIT_ENC_STOP_RSP
0x0075	LM_GLE_STATE_T_GENERATE_SKD_IV
0x0076	LM_GLE_STATE_T_WAIT_ENC_PARAM
0x0077	LM_GLE_STATE_T_CALCULATE_SK
0x0078	LM_GLE_STATE_T_WAIT_ENC_START_RSP
0x0079	LM_GLE_STATE_PHY_UPDATE_IDLE
0x007a	LM_GLE_STATE_PHY_UPDATE_WAIT_IND
0x007b	LM_GLE_STATE_PHY_UPDATE_WAIT_CMP
0x007c	LM_GLE_STATE_WAIT_FEATS_RSP
0x007d	LM_GLE_STATE_MAP_UPDATA_IDLE
0x007e	LM_GLE_STATE_WAIT_MAP_IND_ACK
0x007f	LM_GLE_STATE_WAIT_MAP_IND_ACK_INSTANT_PAST
0x0080	LM_GLE_STATE_WAIT_MAP_INSTANT
0x0081	LM_GLE_STATE_DATA_LENGTH_IDLE
0x0082	LM_GLE_STATE_DATA_LENGTH_WAIT_RSP
0x0083	LM_GLE_STATE_IMG_INITED
0x0084	LM_GLE_STATE_IMG_SETUP
0x0085	LM_GLE_STATE_IMG_WAIT_ACTIVE
0x0086	LM_GLE_STATE_IMG_ACTIVE
0x0087	LM_GLE_STATE_IMG_WAIT_DISC
0x0088	LM_GLE_STATE_IMB_INIT
0x0089	LM_GLE_STATE_IMB_CREATED
0x008a	LM_GLE_STATE_IMB_WAIT_HOST_REPLY
0x008b	LM_GLE_STATE_IMB_DISC_INITD
0x008c	LM_GLE_STATE_IMB_WAIT_DISC_TX_ACK
0x008d	LM_GLE_STATE_IMG_WAIT_SCHED_STOP
0x008e	LM_GLE_STATE_WAIT_VER_RSP
0x008f	LM_GLE_STATE_PWR_CTRL_IDLE
0x0090	LM_GLE_STATE_WAIT_PWR_CTRL_RSP
0x0091	LM_GLE_STATE_ACB_SET_EVT_PARAM_IDLE
0x0092	LM_GLE_STATE_WAIT_ACB_ACCEPT_EVT_PARAM
0x0093	LM_GLE_STATE_ACB_SET_LOW_LATENCY_IDLE
0x0094	LM_GLE_STATE_WAIT_ACB_ACCEPT_LOW_LATENCY
0x0095	LM_GLE_STATE_WAIT_ACB_LOW_LATENCY_IND
0x0096	LM_GLE_STATE_WAIT_ACB_LOW_LATENCY_INSTANT
0x0097	FSM_STATE_BUTT
{PUBLIC END}

{BT BT_CMD START}
0x0406	HCI_DISCONNECT
0x041d	HCI_READ_REMOTE_VERSION_INFORMATION
0x0c01	HCI_SET_EVENT_MASK
0x0c03	HCI_RESET
0x0c13	HCI_WRITE_LOCAL_NAME
0x0c14	HCI_READ_LOCAL_NAME
0x0c31	HCI_SET_CONTROLLER_TO_HOST_FLOW_CONTROL
0x0c33	HCI_HOST_BUFFER_SIZE
0x0c48	HCI_READ_AFH_CHANNEL_ASSESSMENT_MODE
0x0c49	HCI_WRITE_AFH_CHANNEL_ASSESSMENT_MODE
0x0c63	HCI_SET_EVENT_MASK_PAGE_2
0x0c6c	HCI_READ_LE_HOST_SUPPORT
0x0c6d	HCI_WRITE_LE_HOST_SUPPORT
0x1001	HCI_READ_LOCAL_VERSION_INFORMATION
0x1002	HCI_READ_LOCAL_SUPPORTED_COMMANDS
0x1003	HCI_READ_LOCAL_SUPPORTED_FEATURES
0x1009	HCI_READ_BD_ADDR
0x1405	HCI_READ_RSSI
0x2001	HCI_LE_SET_EVENT_MASK
0x2002	HCI_LE_READ_BUFFER_SIZE
0x2003	HCI_LE_READ_LOCAL_SUPPORTED_FEATURES
0x2005	HCI_LE_SET_RANDOM_ADDRESS
0x2006	HCI_LE_SET_ADVERTISING_PARAMETERS
0x2007	HCI_LE_READ_ADVERTISING_CHANNEL_TX_POWER
0x2008	HCI_LE_SET_ADVERTISING_DATA
0x2009	HCI_LE_SET_SCAN_RESPONSE_DATA
0x200a	HCI_LE_SET_ADVERTISING_ENABLE
0x200b	HCI_LE_SET_SCAN_PARAMETERS
0x200c	HCI_LE_SET_SCAN_ENABLE
0x200d	HCI_LE_CREATE_CONNECTION
0x200e	HCI_LE_CREATE_CONNECTION_CANCEL
0x200f	HCI_LE_READ_FILTER_ACCEPT_LIST_SIZE
0x2010	HCI_LE_CLEAR_FILTER_ACCEPT_LIST
0x2011	HCI_LE_ADD_DEVICE_TO_FILTER_ACCEPT_LIST
0x2012	HCI_LE_REMOVE_DEVICE_FROM_FILTER_ACCEPT_LIST
0x2013	HCI_LE_CONNECTION_UPDATE
0x2014	HCI_LE_SET_HOST_CHANNEL_CLASSIFICATION
0x2015	HCI_LE_READ_CHANNEL_MAP
0x2016	HCI_LE_READ_REMOTE_FEATURES
0x2017	HCI_LE_ENCRYPT
0x2018	HCI_LE_RAND
0x2019	HCI_LE_ENABLE_ENCRYPTION
0x201a	HCI_LE_LTK_REQUEST_REPLY
0x201b	HCI_LE_LTK_REQUEST_NEGATIVE_REPLY
0x201c	HCI_LE_READ_SUPPORTED_STATES
0x201d	HCI_LE_RX_TEST_V1
0x201e	HCI_LE_TX_TEST_V1
0x201f	HCI_LE_TEST_END
0x2020	HCI_LE_REM_CONNECTION_PARAM_REQ_REPLY
0x2021	HCI_LE_REM_CONNECTION_PARAM_REQ_NEGATIVE_REPLY
0x2022	HCI_LE_SET_DATA_LENGTH
0x2023	HCI_LE_READ_SUGGESTED_DEFAULT_DATA_LENGTH
0x2024	HCI_LE_WRITE_SUGGESTED_DEFAULT_DATA_LENGTH
0x2025	HCI_LE_READ_LOCAL_P256_PUBLIC_KEY
0x2026	HCI_LE_GENERATE_DHKEY_V1
0x2027	HCI_LE_ADD_DEVICE_TO_RESOLVING_LIST
0x2028	HCI_LE_REMOVE_DEVICE_FROM_RESOLVING_LIST
0x2029	HCI_LE_CLEAR_RESOLVING_LIST
0x202a	HCI_LE_READ_RESOLVING_LIST_SIZE
0x202b	HCI_LE_READ_PEER_RESOLVABLE_ADDRESS
0x202c	HCI_LE_READ_LOCAL_RESOLVABLE_ADDRESS
0x202d	HCI_LE_SET_ADDRESS_RESOLUTION_ENABLE
0x202e	HCI_LE_SET_RESOLVABLE_PRIVATE_ADDRESS_TIMEOUT
0x202f	HCI_LE_READ_MAXIMUM_DATA_LENGTH
0x2030	HCI_LE_READ_PHY
0x2031	HCI_LE_SET_DEFAULT_PHY
0x2032	HCI_LE_SET_PHY
0x2033	HCI_LE_RX_TEST_V2
0x2034	HCI_LE_TX_TEST_V2
0x2035	HCI_LE_SET_ADVERTISING_SET_RANDOM_ADDRESS
0x2036	HCI_LE_SET_EXTENDED_ADVERTISING_PARAMETERS
0x2037	HCI_LE_SET_EXTENDED_ADVERTISING_DATA
0x2038	HCI_LE_SET_EXTENDED_SCAN_RESPONSE_DATA
0x2039	HCI_LE_SET_EXTENDED_ADVERTISING_ENABLE
0x203a	HCI_LE_READ_MAXIMUM_ADVERTISING_DATA_LENGTH
0x203b	HCI_LE_READ_NUMBER_OF_SUPPORTED_ADVERTISING_SETS
0x203c	HCI_LE_REMOVE_ADVERTISING_SET
0x203d	HCI_LE_CLEAR_ADVERTISING_SETS
0x203e	HCI_LE_SET_PERIODIC_ADVERTISING_PARAMETERS
0x203f	HCI_LE_SET_PERIODIC_ADVERTISING_DATA
0x2040	HCI_LE_SET_PERIODIC_ADVERTISING_ENABLE
0x2041	HCI_LE_SET_EXTENDED_SCAN_PARAMETERS
0x2042	HCI_LE_SET_EXTENDED_SCAN_ENABLE
0x2043	HCI_LE_EXTENDED_CREATE_CONNECTION
0x2044	HCI_LE_PERIODIC_ADVERTISING_CREATE_SYNC
0x2045	HCI_LE_PERIODIC_ADVERTISING_CREATE_SYNC_CANCEL
0x2046	HCI_LE_PERIODIC_ADVERTISING_TERMINATE_SYNC
0x2047	HCI_LE_ADD_DEVICE_TO_PERIODIC_ADVERTISER_LIST
0x2048	HCI_LE_REMOVE_DEVICE_FROM_PERIODIC_ADVERTISER_LIST
0x2049	HCI_LE_CLEAR_PERIODIC_ADVERTISER_LIST
0x204a	HCI_LE_READ_PERIODIC_ADVERTISER_LIST_SIZE
0x204b	HCI_LE_READ_TRANSMIT_POWER
0x204c	HCI_LE_READ_RF_PATH_COMPENSATION
0x204d	HCI_LE_WRITE_RF_PATH_COMPENSATION
0x204e	HCI_LE_SET_PRIVACY_MODE
0x204f	HCI_LE_RX_TEST_V3
0x2050	HCI_LE_TX_TEST_V3
0x2051	HCI_LE_SET_CONNECTIONLESS_CTE_TRANSMIT_PARAMETERS
0x2052	HCI_LE_SET_CONNECTIONLESS_CTE_TRANSMIT_ENABLE
0x2058	HCI_LE_READ_ANTENNA_INFORMATION
0x2059	HCI_LE_SET_PERIODIC_ADVERTISING_RECEIVE_ENABLE
0x205e	HCI_LE_GENERATE_DHKEY_V2
0x2076	HCI_LE_ENHANCED_READ_TRANSMIT_POWER_LEVEL
0x2077	HCI_LE_READ_REMOTE_TRANSMIT_POWER_LEVEL
0x2078	HCI_LE_SET_PATH_LOSS_REPORTING_PARAMETERS
0x2079	HCI_LE_SET_PATH_LOSS_REPORTING_ENABLE
0x207a	HCI_LE_SET_TRANSMIT_POWER_REPORTING_ENABLE
0x207b	HCI_LE_TX_TEST_V4
0x2099	HCI_LE_SET_HADM_PARAM
0x209a	HCI_LE_SET_HADM_EN
0x2100	HCI_LE_READ_LOCAL_HADM_CAPS
0x2101	HCI_LE_READ_REMOTE_HADM_CAPS
0x2102	HCI_LE_CREATE_HADM_CONFIG
0x2103	HCI_LE_SET_HADM_PROCEDURE_EN
0xfc32	HCI_VENDOR_SET_BD_ADDR
0xfc4a	HCI_RD_LOCAL_DEV_PRIV_FEAT
0xfc4f	HCI_WR_LOCAL_HOST_PRIV_FEAT
0xfd53	HCI_LE_GET_VENDOR_CAPS
0xfd57	HCI_LE_ADV_FILTER
0xfdb1	HCI_LE_RD_REMOTE_PRIV_FEAT
0xfdb2	HCI_DBG_LE_SET_UHD_PHY
0xfdb3	HCI_DBG_LE_READ_UHD_PHY
0xfdb7	HCI_PRODUCT_LINE_ADPLL_START_TX
0xfdb8	HCI_PRODUCT_LINE_CALI
{PUBLIC END}

{BT GLE_CMD START}
0x0402	HCI_GLE_READ_BUFFER_SIZE
0x0403	HCI_GLE_READ_LOCAL_SUPPORTED_FEATURES
0x0404	HCI_GLE_READ_LOCAL_VERSION_INFORMATION
0x0405	HCI_GLE_SET_PUBLIC_ADDRESS
0x0406	HCI_GLE_GET_PUBLIC_ADDRESS
0x0407	HCI_GLE_SET_RANDOM_ADDRESS
0x0408	HCI_GLE_RESET
0x0409	HCI_GLE_SET_HOST_CHANNEL_CLASSIFICATION
0x040a	HCI_GLE_READ_ACCESS_FILTER_LIST_SIZE
0x040b	HCI_GLE_CLEAR_ACCESS_FILTER_LIST
0x040c	HCI_GLE_ADD_DEVICE_TO_ACCESS_FILTER_LIST
0x040d	HCI_GLE_REMOVE_DEVICE_FROM_ACCESS_FILTER_LIST
0x0c02	HCI_GLE_SET_ADVERTISING_PARAMETERS
0x0c03	HCI_GLE_SET_ADVERTISING_DATA
0x0c04	HCI_GLE_SET_SCAN_RESPONSE_DATA
0x0c05	HCI_GLE_SET_ADVERTISING_ENABLE
0x0c06	HCI_GLE_READ_MAXIMUM_ADVERTISING_DATA_LENGTH
0x0c07	HCI_GLE_READ_NUMBER_OF_SUPPORTED_ADVERTISING_SETS
0x0c08	HCI_GLE_REMOVE_ADVERTISING_SET
0x1001	HCI_GLE_SET_SCAN_PARAMETERS
0x1002	HCI_GLE_SET_SCAN_ENABLE
0x1003	HCI_GLE_SET_SCAN_DATA
0x1401	HCI_GLE_CREATE_CONNECTION
0x1402	HCI_GLE_CREATE_CONNECTION_CANCEL
0x1403	HCI_GLE_DISCONNECT
0x1801	HCI_GLE_READ_REMOTE_FEATURES
0x1802	HCI_GLE_READ_REMOTE_VERSION
0x1804	HCI_GLE_SET_DATA_LENGTH
0x1805	HCI_GLE_READ_PHY
0x1806	HCI_GLE_SET_PHY
0x1807	HCI_GLE_CONNECTION_UPDATE
0x1808	HCI_GLE_REMOTE_CONNECTION_PARAMETER_REQUEST_REPLY
0x1809	HCI_GLE_READ_CHANNEL_MAP
0x180a	HCI_GLE_SET_MCS
0x180c	HCI_GLE_READ_RSSI
0x180d	HCI_GLE_SET_LOCAL_POWER_VALUE
0x180e	HCI_GLE_GET_LOCAL_POWER_VALUE
0x180f	HCI_GLE_GET_REMOTE_POWER_VALUE
0x1810	HCI_GLE_SET_POWER_CHANGE_REPORT_ENABLE
0x1811	HCI_GLE_SET_ACB_EVT_PARAM
0x18ff	HCI_GLE_SET_ACB_LOW_LATENCY
0x1c01	HCI_GLE_ENCRYPT
0x1c02	HCI_GLE_RANDOM
0x1c03	HCI_GLE_ENABLE_ENCRYPTION
0x1c04	HCI_GLE_DISABLE_ENCRYPTION
0x1c05	HCI_GLE_ENC_PARAM_REQUEST_REPLY
0x1c06	HCI_GLE_ENC_PARAM_REQUEST_NEGATIVE_REPLY
0x1c07	HCI_GLE_READ_SUPPORT_CRYPTO_ALGO
0x2c01	HCI_GLE_SET_IMG_PARAMETER
0x2c02	HCI_GLE_CREATE_IMB
0x2c03	HCI_GLE_REMOVE_IMG
0x2c04	HCI_GLE_ACCEPT_IMB_REQUEST
0x2c05	HCI_GLE_REJECT_IMB_REQUEST
0xfc01	HCI_GLE_RX_TEST
0xfc02	HCI_GLE_TX_TEST
0xfc03	HCI_GLE_TEST_END
{PUBLIC END}

{BT BT_EVT START}
0x0005	HCI_DISCONNECTION_COMPLETE
0x0008	HCI_ENCRYPTION_CHANGE_V1
0x000c	HCI_READ_REMOTE_VERSION_INFORMATION_COMPLETE
0x000e	HCI_COMMAND_COMPLETE
0x000f	HCI_COMMAND_STATUS
0x0010	HCI_HARDWARE_ERROR
0x0013	HCI_NUMBER_OF_COMPLETED_PACKETS
0x001a	HCI_DATA_BUFFER_OVERFLOW
0x0030	HCI_ENCRYPTION_KEY_REFRESH_COMPLETE
0x003e	HCI_LE_META_EVENT
0x00ff	HCI_VENDOR_DEBUG_EVENT
{PUBLIC END}

{BT GLE_EVT START}
0x0001	HCI_GLE_COMMAND_STATUS_EVT
0x0002	HCI_GLE_COMMAND_COMPLETE_EVT
0x0003	HCI_GLE_DATA_LENGTH_CHANGE_EVT
0x0004	HCI_GLE_ADVERTISING_TERMINATED
0x0005	HCI_GLE_DISCONNECTION_EVT
0x0007	HCI_GLE_REMOTE_CONNECTION_PARAMETER_REQUEST_EVT
0x0008	HCI_GLE_POWER_CHANGE_REPORT_EVT
0x0009	HCI_GLE_NUMBER_OF_COMPLETED_PACKETS_EVT
0x000a	HCI_GLE_HARDWARE_ERROR_EVT
0x000b	HCI_GLE_DATA_BUFFER_OVERFLOW_EVT
0x000c	HCI_GLE_IMB_REQUEST_EVT
0x000d	HCI_GLE_IMB_ESTABLISHED_EVT
0x000e	HCI_GLE_ENCRYPTION_PARAMETER_REQUEST_EVT
0x0011	HCI_GLE_ENCRYPTION_CHANGE_EVT
0x0012	HCI_GLE_ACB_RETRY_EVT
0x0013	HCI_GLE_ACB_LOW_LATENCY_EN_EVT
0x1401	HCI_GLE_CONNECTION_EVT
0x1801	HCI_GLE_READ_REMOTE_FEATURES_EVT
0x1802	HCI_GLE_READ_REMOTE_VERSION_EVT
0x1805	HCI_GLE_READ_PHY_EVT
0x1806	HCI_GLE_PHY_UPDATE_EVT
0x1807	HCI_GLE_CONNECTION_UPDATE_EVT
0x180b	HCI_GLE_ADVERTISING_REPORT_EVT
0x180f	HCI_GLE_GET_REMOTE_POWER_VALUE_EVT
0x1811	HCI_GLE_SET_ACB_EVT_PARAM_EVT
0x18ff	HCI_GLE_SET_ACB_LOW_LATENCY_EVT
{PUBLIC END}

{BT BLE_SUB_EVT START}
0x0001	HCI_LE_CONNECTION_COMPLETE
0x0002	HCI_LE_ADVERTISING_REPORT
0x0003	HCI_LE_CONNECTION_UPDATE_COMPLETE
0x0004	HCI_LE_READ_REMOTE_FEATURES_COMPLETE
0x0005	HCI_LE_LONG_TERM_KEY_REQUEST
0x0006	HCI_LE_REMOTE_CONNECTION_PARAMETER_REQUEST
0x0007	HCI_LE_DATA_LENGTH_CHANGE
0x0008	HCI_LE_READ_LOCAL_P256_PUBLIC_KEY_COMPLETE
0x0009	HCI_LE_GENERATE_DHKEY_COMPLETE
0x000a	HCI_LE_ENHANCED_CONNECTION_COMPLETE
0x000b	HCI_LE_DIRECTED_ADVERTISING_REPORT
0x000c	HCI_LE_PHY_UPDATE_COMPLETE
0x000d	HCI_LE_EXTENDED_ADVERTISING_REPORT
0x000e	HCI_LE_PERIODIC_ADVERTISING_SYNC_ESTABLISHED
0x000f	HCI_LE_PERIODIC_ADVERTISING_REPORT
0x0010	HCI_LE_PERIODIC_ADVERTISING_SYNC_LOST
0x0011	HCI_LE_EXTENDED_SCAN_TIMEOUT
0x0012	HCI_LE_ADVERTISING_SET_TERMINATED
0x0013	HCI_LE_SCAN_REQUEST_RECEIVED
0x0014	HCI_LE_CHANNEL_SELECTION_ALGORITHM
0x0020	HCI_LE_PATH_LOSS_THRESHOLD
0x0021	HCI_LE_TRANSMIT_POWER_REPORTING
0x005d	HCI_LE_HADM_PROC_RESULT
0x005e	HCI_LE_HADM_CONFIG
0x005f	HCI_LE_HADM_CAPS
0x0070	HCI_LE_RD_REMOTE_PRIV_FEAT_COMPLETE
0x0071	HCI_LE_UHD_PHY_UPDATE_COMPLETE
{PUBLIC END}

{BT BLE_LLCP START}
0x0000	LL_CONNECTION_UPDATE_IND
0x0001	LL_CHANNEL_MAP_IND
0x0002	LL_TERMINATE_IND
0x0003	LL_ENC_REQ
0x0004	LL_ENC_RSP
0x0005	LL_START_ENC_REQ
0x0006	LL_START_ENC_RSP
0x0007	LL_UNKNOWN_RSP
0x0008	LL_FEATURE_REQ
0x0009	LL_FEATURE_RSP
0x000a	LL_PAUSE_ENC_REQ
0x000b	LL_PAUSE_ENC_RSP
0x000c	LL_VERSION_IND
0x000d	LL_REJECT_IND
0x000e	LL_PERIPHERAL_FEATURE_REQ
0x000f	LL_CONNECTION_PARAM_REQ
0x0010	LL_CONNECTION_PARAM_RSP
0x0011	LL_REJECT_EXT_IND
0x0012	LL_PING_REQ
0x0013	LL_PING_RSP
0x0014	LL_LENGTH_REQ
0x0015	LL_LENGTH_RSP
0x0016	LL_PHY_REQ
0x0017	LL_PHY_RSP
0x0018	LL_PHY_UPDATE_IND
0x0019	LL_MIN_USED_CHANNELS_IND
0x0023	LL_POWER_CONTROL_REQ
0x0024	LL_POWER_CONTROL_RSP
0x0025	LL_POWER_CHANGE_IND
0x0028	LL_CHANNEL_REPORTING_IND
0x0029	LL_CHANNEL_STATUS_IND
0x0039	LLCP_HADM_V2_REQ
0x0040	LLCP_HADM_V2_RSP
0x0041	LLCP_HADM_IND
0x0042	LLCP_HADM_CAPS_IND
0x0043	LLCP_HADM_CONFIG_REQ
0x0044	LLCP_HADM_CONFIG_RES
0x0091	LL_PRIV_FEATURE_IND
0x0092	LL_PRIV_FEATURE_REQ
0x0093	LL_PRIV_FEATURE_RSP
0x0094	LL_UHD_REQ
0x0095	LL_UHD_RSP
0x0096	LL_UHD_UPDATE_IND
0x0097	LL_MAX
{PUBLIC END}

{BT GLE_LLCP START}
0x0003	GLE_LL_REJECT_IND
0x0004	GLE_LL_ENC_REQ
0x0005	GLE_LL_ENC_RSP
0x0006	GLE_LL_START_ENC_REQ
0x0007	GLE_LL_START_ENC_RSP
0x0008	GLE_LL_PAUSE_ENC_REQ
0x0009	GLE_LL_PAUSE_ENC_RSP
0x000a	GLE_LL_FEATURE_REQ
0x000b	GLE_LL_FEATURE_RSP
0x000c	GLE_LL_UNKNOWN_RSP
0x000d	GLE_LL_VERSION_IND
0x000e	GLE_LL_LENGTH_REQ
0x000f	GLE_LL_LENGTH_RSP
0x0010	GLE_LL_CHANNEL_REPORTING_IND
0x0011	GLE_LL_CHANNEL_STATUS_IND
0x0012	GLE_LL_CHANNEL_FREQ_TABLE_IND
0x0013	GLE_LL_CHANNEL_MAP_IND
0x0014	GLE_LL_MIN_USED_CHANNELS_IND
0x0017	GLE_LL_PHY_UPDATE_REQ
0x0018	GLE_LL_PHY_UPDATE_IND
0x0019	GLE_LL_POWER_CONTROL_REQ
0x001a	GLE_LL_POWER_CONTROL_RSP
0x001b	GLE_LL_POWER_CHANGE_IND
0x001e	GLE_LL_TERMINATE_IND
0x0020	GLE_LL_CONNECTION_PARAM_REQ
0x0021	GLE_LL_CONNECTION_PARAM_RSP
0x0022	GLE_LL_IMB_CONNECT_IND
0x003a	GLE_LL_CONNECTION_UPDATE_IND
0x003d	GLE_LL_IMB_TERMINATE_IND
0x004c	GLE_LL_IOB_TERMINATE_IND
0x00f0	GLE_LL_SET_ACB_EVT_PARAM
0x00f1	GLE_LL_ACCEPT_ACB_EVT_PARAM
0x00f2	GLE_LL_SET_ACB_LOW_LATENCY
0x00f3	GLE_LL_ACCEPT_ACB_LOW_LATENCY
0x00f4	GLE_LL_ACB_LOW_LATENCY_INSTANT_IND
0xffff	GLE_LL_INVALID
{PUBLIC END}

{BT BLE_ACL_DATA START}
0x0000	MSG_SEND_BLE_ACL_DATA
0x0001	MSG_RECV_BLE_ACL_DATA
{PUBLIC END}

{BT PRIVATE START}
0x0000	MSG_TASK_EVENT_INT
0x0001	MSG_TASK_END
0x0002	MSG_TASK_LSTO
0x0003	MSG_TASK_SET_TASK_PARAM
0x0004	MSG_TASK_SEND_LMP_DATA
0x0005	MSG_TASK_SEND_LLCP_DATA
0x0006	MSG_TASK_SEND_GLCP_DATA
0x0007	MSG_TASK_SEND_SYNC_DATA
0x0008	MSG_TASK_RECV_LMP_DATA
0x0009	MSG_TASK_RECV_GLCP_DATA
0x000a	MSG_TASK_RECV_SYNC_DATA
0x000b	MSG_HCI_RETRY_TX
0x000c	MSG_DM_CO_AFH_CHNL_ASSESS_MODE_CHANGED
0x000d	MSG_DM_BLE_HOST_SET_CHNL_INTERVAL_TO
0x000e	MSG_DM_BLE_HOST_SET_CHNL_CHANGED
0x000f	MSG_DM_BLE_ADDR_RESOLUTION_TO
0x0010	MSG_BLE_ADV_CONNECT_REQ_RECEIVED
0x0011	MSG_BLE_ADV_SCAN_REQ_RECEIVED
0x0012	MSG_BLE_ADV_EVT_ADV_END
0x0013	MSG_BLE_ADV_EVT_ADV_STOP
0x0014	MSG_BLE_ADV_SET_EXT_EN
0x0015	MSG_BLE_ADV_DISPATCH_ACK
0x0016	MSG_BLE_EXT_ADV_ENABLED
0x0017	MSG_BLE_PER_ADV_ENABLED
0x0018	MSG_BLE_PER_ADV_DISABLED
0x0019	MSG_BLE_PER_SYNC_FINISHED
0x001a	MSG_BLE_PER_SYNC_TERMINATED
0x001b	MSG_BLE_PER_SYNC_DATA_REPORT
0x001c	MSG_BLE_PER_SYNC_INFO
0x001d	MSG_BLE_ACL_TERMINATE_ACK
0x001e	MSG_BLE_ACL_DISC_WITH_TERMINATE
0x001f	MSG_BLE_ACL_DISC_WITHOUT_TERMINATE
0x0020	MSG_BLE_ACL_EVENT_SCHED_STOP
0x0021	MSG_BLE_ACL_CONN_UPDATE_REQ
0x0022	MSG_BLE_ACL_CONN_UPDATE_CMP
0x0023	MSG_BLE_ACL_SEND_CHNL_REPORT
0x0024	MSG_BLE_ACL_CHANNEL_MAP_ACK
0x0025	MSG_BLE_ACL_CHANNEL_MAP_ACK_TO
0x0026	MSG_BLE_ACL_REQ_MAP_UPDATE
0x0027	MSG_BLE_ACL_REQ_CHNL_STATUS_TIMER
0x0028	MSG_BLE_ACL_MAP_UPDATE_INSTANT_DIFF_ONE
0x0029	MSG_BLE_ACL_MAP_UPDATE_INSTANT_CMP
0x002a	MSG_BLE_ACL_PHY_AUTO_UPD
0x002b	MSG_BLE_ACL_PHY_UPD_INST_CMP
0x002c	MSG_BLE_ACL_UHD_UPD_INST_CMP
0x002d	MSG_BLE_ACL_TERMINATE_TO
0x002e	MSG_BLE_ACL_PROC_RSP_TO
0x002f	MSG_BLE_ACL_RD_REM_FEATS_TO
0x0030	MSG_BLE_ACL_RD_REM_VER_TO
0x0031	MSG_BLE_ACL_CONNECTION_TO
0x0032	MSG_BLE_ACL_CONN_UPDATE_TO
0x0033	MSG_BLE_ACL_PHY_UPD_TO
0x0034	MSG_BLE_ACL_UHD_UPD_TO
0x0035	MSG_BLE_ACL_PWR_CTRL_LLCP_TO
0x0036	MSG_BLE_ACL_PATH_LOSS_THRES_REP_TO
0x0037	MSG_BLE_ACL_PWR_CTRL_IND
0x0038	MSG_BLE_ACL_START_DATA_LENGTH_UPDATE
0x0039	MSG_BLE_ACL_DATA_LENGTH_SEND_REQ
0x003a	MSG_BLE_ACL_DATA_LENGTH_RSP_TO
0x003b	MSG_TASK_BLE_ACL_EVENT_ABNORMAL
0x003c	MSG_GLE_ACB_ESTABLISH_TO
0x003d	MSG_GLE_ACB_CONNECTION_TO
0x003e	MSG_TASK_GLE_ACB_EVENT_ABNORMAL
0x003f	MSG_GLE_ACB_TERMINATE_ACK
0x0040	MSG_GLE_ACB_DISC_WITH_TERMINATE
0x0041	MSG_GLE_ACB_DISC_WITHOUT_TERMINATE
0x0042	MSG_GLE_ACB_EVENT_SCHED_STOP
0x0043	MSG_GLE_ACB_TERMINATE_TO
0x0044	MSG_GLE_IOB_TERMINATE_TO
0x0045	MSG_GLE_IOB_ESTABLISH_TO
0x0046	MSG_GLE_IOB_CONNECTION_TO
0x0047	MSG_TASK_GLE_IOB_EVENT_ABNORMAL
0x0048	MSG_GLE_IOB_TERMINATE_ACK
0x0049	MSG_GLE_IOB_DISC_WITH_TERMINATE
0x004a	MSG_GLE_IOB_DISC_WITHOUT_TERMINATE
0x004b	MSG_GLE_IOB_EVENT_SCHED_STOP
0x004c	MSG_ACTIVE_TIMER_SCHEDULER
0x004d	MSG_KEY_DERIV_ENCRYPT_START
0x004e	MSG_KEY_EXCHANGE_ENCRYPT_START
0x004f	MSG_KEY_DERIV_ENCRYPT_DONE
0x0050	MSG_KEY_EXCHANGE_ENCRYPT_DONE
0x0051	MSG_BLE_SCAN_SCHED_STOP
0x0052	MSG_BLE_SCAN_EVT_TASK_REPORT_DATA
0x0053	MSG_BLE_SCAN_EVT_TASK_FSM_START
0x0054	MSG_BLE_EXTENDED_SCAN_DURATION_TO
0x0055	MSG_BLE_EXTENDED_SCAN_RESTART
0x0056	MSG_BLE_INITIATE_SCHED_STOP
0x0057	MSG_BLE_INITIATE_EVT_TASK_REPORT_DATA
0x0058	MSG_BLE_INITIATE_EVT_TASK_FSM_START
0x0059	MSG_BLE_TEST_MODE_END
0x005a	MSG_BLE_ACL_RD_REM_PRIV_FEAT_TO
0x005b	MSG_BLE_CS_RPT_RESULT
0x005c	MSG_BLE_CS_EVT_END
0x005d	MSG_CS_OPERATION_TO
0x005e	MSG_DPC_START
0x005f	MSG_DPC_SAMPLE_MSG
0x0060	MSG_DPC_ES_MSG
0x0061	MSG_DPC_ES_CANCEL_PROCESS_MSG
0x0062	MSG_DPC_END
0x0063	MSG_COEX_MAP_UPDATE
0x0064	MSG_GLE_SCAN_SCHED_STOP
0x0065	MSG_GLE_SCAN_EVT_TASK_REPORT_DATA
0x0066	MSG_GLE_SCAN_EVT_TASK_FSM_START
0x0067	MSG_GLE_SCAN_DURATION_TO
0x0068	MSG_GLE_SCAN_RESTART
0x0069	MSG_GLE_INITIATE_SCHED_STOP
0x006a	MSG_GLE_INITIATE_EVT_TASK_REPORT_DATA
0x006b	MSG_GLE_INITIATE_EVT_TASK_FSM_START
0x006c	MSG_GLE_ADV_CONNECT_REQ_RECEIVED
0x006d	MSG_GLE_ADV_SET_EN
0x006e	MSG_GLE_ADV_DISPATCH_ACK
0x006f	MSG_GLE_TEST_MODE_END
0x0070	MSG_SLEEP_DEFER
0x0071	MSG_BLE_START_ENC_RSP_ACK
0x0072	MSG_GLE_ADV_SCAN_REQ_RECEIVED
0x0073	MSG_GLE_ACB_CONN_UPDATE_REQ
0x0074	MSG_GLE_ACB_CONN_UPDATE_CMP
0x0075	MSG_GLE_ACB_CONN_UPDATE_TO
0x0076	MSG_GLE_ACB_PHY_UPDATE_AUTO_REQ
0x0077	MSG_GLE_ACB_PHY_UPDATE_CMP
0x0078	MSG_GLE_ACB_PHY_UPDATE_TO
0x0079	MSG_GLE_ACB_RD_REM_FEATS_TO
0x007a	MSG_GLE_IMB_INSTANT
0x007b	MSG_GLE_ACB_CHANNEL_MAP_ACK
0x007c	MSG_GLE_ACB_CHANNEL_MAP_ACK_TO
0x007d	MSG_GLE_ACB_REQ_MAP_UPDATE
0x007e	MSG_GLE_ACB_MAP_UPDATE_INSTANT_CMP
0x007f	MSG_DM_GLE_HOST_SET_CHNL_INTERVAL_TO
0x0080	MSG_DM_GLE_HOST_SET_CHNL_CHANGED
0x0081	MSG_GLE_ACB_RD_REM_VER_TO
0x0082	MSG_GLE_ACB_ENC_PROC_RSP_TO
0x0083	MSG_GLE_ACB_START_DATA_LENGTH_UPDATE
0x0084	MSG_GLE_ACB_DATA_LENGTH_RSP_TO
0x0085	MSG_GLE_IMB_WAIT_FIRST_PDU_TO
0x0086	MSG_TASK_GLE_IMB_EVENT_ABNORMAL
0x0087	MSG_GLE_IMB_DISC_WITH_TERMINATE
0x0088	MSG_GLE_IMB_DISC_WITHOUT_TERMINATE
0x0089	MSG_GLE_IMG_EVENT_SCHED_STOP
0x008a	MSG_GLE_IMB_TERMINATE_TO
0x008b	MSG_GLE_IMB_TERMINATE_ACK
0x008c	MSG_GLE_IMG_ADD_ACTIVE_IMB
0x008d	MSG_GLE_IMG_ADD_ACTIVE_IMB_FAIL
0x008e	MSG_GLE_IMG_DISC_ACTIVE_IMB
0x008f	MSG_GLE_ACB_CREATE_IMB
0x0090	MSG_GLE_ACB_DISCONNECT_IMB
0x0091	MSG_GLE_ACB_TERMINATE
0x0092	MSG_GLE_ACB_PWR_CTRL_IND
0x0093	MSG_GLE_ACB_PWR_CTRL_LLCP_TO
0x0094	MSG_GLE_ACB_START_EVT_PARAM_UPDATE
0x0095	MSG_GLE_ACB_ACCEPT_EVT_PARAM_TO
0x0096	MSG_ENCRYPT_EXECUTE_TASK_TRIGGER
0x0097	MSG_GLE_ACB_START_LOW_LATENCY_UPDATE
0x0098	MSG_GLE_ACB_LOW_LATENCY_TO
0x0099	MSG_GLE_ACB_LOW_LATENCY_INSTANT_CMP
0x009a	MSG_GLE_ADV_SCHED_STOP
0x009b	MSG_DM_CLOCK_GT_MODE_TO
{PUBLIC END}

{BT GLE_ACB_DATA START}
0x0000	MSG_SEND_GLE_ACB_DATA
0x0001	MSG_RECV_GLE_ACB_DATA
0x0002	MSG_SEND_GLE_ACB_EM_DATA
{PUBLIC END}

{BT GLE_IOB_DATA START}
0x0000	MSG_SEND_GLE_IOB_DATA
0x0001	MSG_RECV_GLE_IOB_DATA
{PUBLIC END}

{BT GLE_IMB_DATA START}
0x0000	MSG_SEND_GLE_IMB_DATA
0x0001	MSG_RECV_GLE_IMB_DATA
0x0002	MSG_SEND_GLE_IMB_EM_DATA
{PUBLIC END}

=================================================================================================================================================================

//BT fsm_send_msg
[MsgID=0x0100;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BT_CMD
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0101;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_CMD
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0102;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BT_EVT
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0103;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_EVT
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0104;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0106;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0107;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_ACL_DATA
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x010B;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT PRIVATE
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x010C;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_ACB_DATA
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x010D;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_IOB_DATA
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x010E;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_IMB_DATA
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0180;Parameter Number=4;%s;---->%s; send sed_id=%d; HCI_COMMAND_COMPLETE,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BT_CMD
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0181;Parameter Number=4;%s;---->%s; send sed_id=%d; HCI_COMMAND_STATUS,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BT_CMD
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0182;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_SUB_EVT
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0183;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0184;Parameter Number=4;%s;---->%s; send sed_id=%d; LL_UNKNOWN_RSP,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter4_CommandList End

//BT fsm_send_msg
[MsgID=0x0185;Parameter Number=4;%s;---->%s; send sed_id=%d; LL_REJECT_EXT_IND,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0186;Parameter Number=4;%s;---->%s; send sed_id=%d; HCI_GLE_COMMAND_COMPLETE,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_CMD
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0187;Parameter Number=4;%s;---->%s; send sed_id=%d; HCI_GLE_COMMAND_STATUS,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_CMD
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0188;Parameter Number=4;%s;---->%s; send sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x0189;Parameter Number=4;%s;---->%s; send sed_id=%d; GLE_LL_UNKNOWN_RSP,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter4_CommandList End

//GLE fsm_send_msg
[MsgID=0x018A;Parameter Number=4;%s;---->%s; send sed_id=%d; GLE_LL_REJECT_IND,  %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter4_CommandList End

//BT fsm_msg_forward
[MsgID=0x0200;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BT_CMD
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x0201;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_CMD
Parameter4_CommandList End

//BT fsm_msg_forward
[MsgID=0x0202;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BT_EVT
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x0203;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_EVT
Parameter4_CommandList End

//BT fsm_msg_forward
[MsgID=0x0204;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x0206;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter4_CommandList End

//BT fsm_msg_forward
[MsgID=0x0207;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT BLE_ACL_DATA
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x020B;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT PRIVATE
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x020C;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_ACB_DATA
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x020D;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_IOB_DATA
Parameter4_CommandList End

//GLE fsm_msg_forward
[MsgID=0x020E;Parameter Number=4;%s;---->%s; forward sed_id=%d; %s]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
$$BT GLE_IMB_DATA
Parameter4_CommandList End

//BT fsm_msg_save_to_head_ext
[MsgID=0x0300;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BT_CMD
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_head_ext
[MsgID=0x0301;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_CMD
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_head_ext
[MsgID=0x0302;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BT_EVT
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_head_ext
[MsgID=0x0303;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_EVT
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_head_ext
[MsgID=0x0304;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_head_ext
[MsgID=0x0306;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_head_ext
[MsgID=0x0307;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BLE_ACL_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_head_ext
[MsgID=0x030B;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT PRIVATE
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_head_ext
[MsgID=0x030C;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_ACB_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_head_ext
[MsgID=0x030D;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_IOB_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_head_ext
[MsgID=0x030E;Parameter Number=4;%s; sed_id=%d; SAVED HEAD %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_IMB_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_tail_ext
[MsgID=0x0400;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BT_CMD
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_tail_ext
[MsgID=0x0401;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_CMD
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_tail_ext
[MsgID=0x0402;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BT_EVT
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_tail_ext
[MsgID=0x0403;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_EVT
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_tail_ext
[MsgID=0x0404;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BLE_LLCP
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_tail_ext
[MsgID=0x0406;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_LLCP
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_tail_ext
[MsgID=0x0407;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT BLE_ACL_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_save_to_tail_ext
[MsgID=0x040B;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT PRIVATE
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_tail_ext
[MsgID=0x040C;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_ACB_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_tail_ext
[MsgID=0x040D;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_IOB_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//GLE fsm_msg_save_to_tail_ext
[MsgID=0x040E;Parameter Number=4;%s; sed_id=%d; SAVED TAIL %s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT GLE_IMB_DATA
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_restore_to_head_ext
[MsgID=0x0801;Parameter Number=4;%s; sed_id=%d; RESTORE HEAD file=%s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT CFILE
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_msg_restore_to_tail_ext
[MsgID=0x0802;Parameter Number=4;%s; sed_id=%d; RESTORE TAIL file=%s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT CFILE
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End

//BT fsm_trans_state_ext
[MsgID=0x0810;Parameter Number=4;%s; sed_id=%d; TRANS_STATE=%s; linenum=%d]
Parameter1_CommandList Start:
Index	Show
$$BT FSM_TYPE
Parameter1_CommandList End
Parameter2_CommandList Start:
Index	Show
Parameter2_CommandList End
Parameter3_CommandList Start:
Index	Show
$$BT FSM_STATE
Parameter3_CommandList End
Parameter4_CommandList Start:
Index	Show
Parameter4_CommandList End
