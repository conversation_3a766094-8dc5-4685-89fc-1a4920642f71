/**
 * @file sle_keyboard_dongle.c
 * @brief SLE Keyboard Dongle Source / SLE键盘适配器源代码
 * @copyright Copyright (c) @CompanyNameMagicTag 2023-2023. All rights reserved.
 *
 * Implements the SLE keyboard dongle functionality / 实现SLE键盘适配器功能
 *
 * <AUTHOR>
 *
 * @date 2023-07-28
 * - Initial version / 初始版本
 */
#include "securec.h"
#include "chip_io.h"
#include "cmsis_os2.h"
#include "common_def.h"
#include "app_init.h"
#include "gadget/f_hid.h"
#include "osal_debug.h"
#include "osal_task.h"
#include "implementation/usb_init.h"
#include "sle_connection_manager.h"
#include "sle_ssap_client.h"
#include "sle_keyboard_client.h"
#include "sle_keyboard_hid.h"

#define SLE_KEYBOARD_DONGLE_TASK_STACK_SIZE 0x1000
/**
 * @brief SLE keyboard dongle task priority / SLE键盘适配器任务优先级
 */
#define SLE_KEYBOARD_DONGLE_TASK_PRIO (osPriority_t)(17)
/**
 * @brief SLE keyboard dongle task delay in milliseconds / SLE键盘适配器任务延迟毫秒数
 */
#define SLE_KEYBOARD_DONGLE_TASK_DELAY_MS 2000
/**
 * @brief USB HID keyboard initialization delay / USB HID键盘初始化延迟
 */
#define USB_HID_KEYBOARD_INIT_DELAY_MS (500UL)
/**
 * @brief USB keyboard reporter length / USB键盘报告长度
 */
#define USB_KEYBOARD_REPORTER_LEN 9
/**
 * @brief SLE keyboard USB manufacturer string / SLE键盘USB制造商字符串
 */
#define SLE_KRYBOARD_USB_MANUFACTURER                                          \
    {                                                                          \
        'H', 0, 'H', 0, 'H', 0, 'H', 0, 'l', 0, 'i', 0, 'c', 0, 'o', 0, 'n', 0 \
    }
/**
 * @brief SLE keyboard USB manufacturer string length / SLE键盘USB制造商字符串长度
 */
#define SLE_KRYBOARD_USB_MANUFACTURER_LEN 20
/**
 * @brief SLE keyboard USB product string / SLE键盘USB产品字符串
 */
#define SLE_KRYBOARD_USB_PRODUCT                                                       \
    {                                                                                  \
        'H', 0, 'H', 0, '6', 0, '6', 0, '6', 0, '6', 0, ' ', 0, 'U', 0, 'S', 0, 'B', 0 \
    }
/**
 * @brief SLE keyboard USB product string length / SLE键盘USB产品字符串长度
 */
#define SLE_KRYBOARD_USB_PRODUCT_LEN 22
/**
 * @brief SLE keyboard USB serial string / SLE键盘USB序列号字符串
 */
#define SLE_KRYBOARD_USB_SERIAL                                        \
    {                                                                  \
        '2', 0, '0', 0, '2', 0, '0', 0, '0', 0, '6', 0, '2', 0, '4', 0 \
    }
/**
 * @brief SLE keyboard USB serial string length / SLE键盘USB序列号字符串长度
 */
#define SLE_KRYBOARD_USB_SERIAL_LEN 16
/**
 * @brief Maximum receive length / 最大接收长度
 */
#define RECV_MAX_LENGTH 13
/**
 * @brief USB receive stack size / USB接收栈大小
 */
#define USB_RECV_STACK_SIZE 0x400
/**
 * @brief Log tag for SLE keyboard dongle / SLE键盘适配器日志标签
 */
#define SLE_KEYBOARD_DONGLE_LOG "[sle keyboard dongle]"

/**
 * @brief SLE keyboard dongle initialization flag / SLE键盘适配器初始化标志
 */
static bool g_sle_keyboard_dongle_inited = false;
/**
 * @brief SLE keyboard dongle HID index / SLE键盘适配器HID索引
 */
static uint32_t g_sle_keyboard_dongle_hid_index = 0;

/**
 * @brief Send data through SLE keyboard dongle / 通过SLE键盘适配器发送数据
 * @param[in] rpt Pointer to USB HID keyboard report / USB HID键盘报告指针
 */
static void sle_keyboard_dongle_send_data(usb_hid_keyboard_report_t *rpt)
{
    if (rpt == NULL) {
        return;
    }
    rpt->kind = 0x01;
    int32_t ret = fhid_send_data(g_sle_keyboard_dongle_hid_index, (char *)rpt, USB_KEYBOARD_REPORTER_LEN);
    if (ret == -1) {
        osal_printk("%s send data falied! ret:%d\n", SLE_KEYBOARD_DONGLE_LOG, ret);
        return;
    }
}

/**
 * @brief Handle sending data to server / 处理向服务器发送数据
 * @param[in] buffer Data buffer / 数据缓冲区
 * @param[in] length Data length / 数据长度
 */
static void sle_keyboard_send_to_server_handler(const uint8_t *buffer, uint16_t length)
{
    ssapc_write_param_t g_sle_keyboard_send_param = get_sle_keyboard_send_param();
    uint16_t g_sle_keyboard_conn_id = get_sle_keyboard_conn_id();
    g_sle_keyboard_send_param.data_len = length;
    g_sle_keyboard_send_param.data = (uint8_t *)buffer;
    ssapc_write_req(0, g_sle_keyboard_conn_id, &g_sle_keyboard_send_param);
    osal_printk("%s sle keyboard send data ,len: %d\r\n", SLE_KEYBOARD_DONGLE_LOG, length);
}

/**
 * @brief USB receive task for SLE keyboard dongle / SLE键盘适配器USB接收任务
 * @param[in] para Task parameter / 任务参数
 * @return Task result / 任务结果
 */
static void *sle_keyboard_dongle_usb_recv_task(const char *para)
{
    UNUSED(para);
    uint8_t recv_hid_data[RECV_MAX_LENGTH];

    osal_printk("%s enter sle_keyboard_dongle_usb_recv_task!\r\n", SLE_KEYBOARD_DONGLE_LOG);
    while (1) {
        int32_t ret = fhid_recv_data(g_sle_keyboard_dongle_hid_index, (char *)recv_hid_data, RECV_MAX_LENGTH);
        if (ret <= 0) {
            osal_msleep(SLE_KEYBOARD_DONGLE_TASK_DELAY_MS);
        }
        osal_printk("%s keyboard recv data from pc, len = [%d], data: \r\n", SLE_KEYBOARD_DONGLE_LOG, ret);
        for (int i = 0; i < ret; i++) {
            osal_printk("0x%02x ", recv_hid_data[i]);
        }
        osal_printk("\r\n");
        sle_keyboard_send_to_server_handler(recv_hid_data, ret);
    }
    osal_printk("%s usb recv task over\r\n", SLE_KEYBOARD_DONGLE_LOG);
    return NULL;
}

/**
 * @brief Internal initialization for SLE keyboard dongle / SLE键盘适配器内部初始化
 * @param[in] dtype Device type / 设备类型
 * @return Initialization result / 初始化结果
 * @retval SLE_KEYBOARD_DONGLE_OK Success / 成功
 * @retval SLE_KEYBOARD_DONGLE_FAILED Failure / 失败
 */
static uint8_t sle_keyboard_dongle_init_internal(device_type dtype)
{
    if (g_sle_keyboard_dongle_inited) {
        return SLE_KEYBOARD_DONGLE_OK;
    }

    const char manufacturer[SLE_KRYBOARD_USB_MANUFACTURER_LEN] = SLE_KRYBOARD_USB_MANUFACTURER;
    struct device_string str_manufacturer = {.str = manufacturer, .len = SLE_KRYBOARD_USB_MANUFACTURER_LEN};

    const char product[SLE_KRYBOARD_USB_PRODUCT_LEN] = SLE_KRYBOARD_USB_PRODUCT;
    struct device_string str_product = {.str = product, .len = SLE_KRYBOARD_USB_PRODUCT_LEN};

    const char serial[SLE_KRYBOARD_USB_SERIAL_LEN] = SLE_KRYBOARD_USB_SERIAL;
    struct device_string str_serial_number = {.str = serial, .len = SLE_KRYBOARD_USB_SERIAL_LEN};

    struct device_id dev_id = {.vendor_id = 0x1111, .product_id = 0x0009, .release_num = 0x0800};

    if (dtype == DEV_HID) {
        g_sle_keyboard_dongle_hid_index = sle_keyboard_dongle_set_report_desc_hid();
    }

    if (usbd_set_device_info(dtype, &str_manufacturer, &str_product, &str_serial_number, dev_id) != 0) {
        osal_printk("%s set device info fail!\r\n", SLE_KEYBOARD_DONGLE_LOG);
        return SLE_KEYBOARD_DONGLE_FAILED;
    }

    if (usb_init(DEVICE, dtype) != 0) {
        osal_printk("%s usb_init failed!\r\n", SLE_KEYBOARD_DONGLE_LOG);
        return SLE_KEYBOARD_DONGLE_FAILED;
    }
    osal_kthread_create((void *)sle_keyboard_dongle_usb_recv_task, NULL, "sle_keyboard_dongle_recv",
                        USB_RECV_STACK_SIZE);
    g_sle_keyboard_dongle_inited = true;
    return SLE_KEYBOARD_DONGLE_OK;
}

/**
 * @brief Initialize SLE keyboard dongle / 初始化SLE键盘适配器
 * @return Initialization result / 初始化结果
 * @retval SLE_KEYBOARD_DONGLE_OK Success / 成功
 * @retval SLE_KEYBOARD_DONGLE_FAILED Failure / 失败
 */
static uint8_t sle_keyboard_dongle_init(void)
{
    if (!g_sle_keyboard_dongle_inited) {
        if (sle_keyboard_dongle_init_internal(DEV_HID) != SLE_KEYBOARD_DONGLE_OK) {
            return SLE_KEYBOARD_DONGLE_FAILED;
        }
        osDelay(USB_HID_KEYBOARD_INIT_DELAY_MS);
    }
    return SLE_KEYBOARD_DONGLE_OK;
}

/**
 * @brief Notification callback for SLE keyboard / SLE键盘通知回调
 * @param[in] client_id Client ID / 客户端ID
 * @param[in] conn_id Connection ID / 连接ID
 * @param[in] data Handle value data / 句柄值数据
 * @param[in] status Operation status / 操作状态
 */
static void sle_keyboard_notification_cb(uint8_t client_id,
                                         uint16_t conn_id,
                                         ssapc_handle_value_t *data,
                                         errcode_t status)
{
    unused(client_id);
    unused(conn_id);
    unused(status);
    usb_hid_keyboard_report_t *recv_usb_hid_keyboard = NULL;
    if (data == NULL || data->data_len == 0 || data->data == NULL) {
        osal_printk("%s sle_keyboard_notification_cb fail, recv data is null!\r\n", SLE_KEYBOARD_DONGLE_LOG);
    }
    osal_printk("%s sle keyboard recive notification\r\n", SLE_KEYBOARD_DONGLE_LOG);
    recv_usb_hid_keyboard = (usb_hid_keyboard_report_t *)data->data;
    osal_printk("%s recv_usb_hid_keyboard.kind = [%d]\r\n", SLE_KEYBOARD_DONGLE_LOG, recv_usb_hid_keyboard->kind);
    osal_printk("%s recv_usb_hid_keyboard.special_key = [%d]\r\n", SLE_KEYBOARD_DONGLE_LOG,
                recv_usb_hid_keyboard->special_key);
    osal_printk("%s recv_usb_hid_keyboard.reversed = [%d]\r\n", SLE_KEYBOARD_DONGLE_LOG,
                recv_usb_hid_keyboard->reversed);
    osal_printk("%s recv_usb_hid_keyboard.key = ", SLE_KEYBOARD_DONGLE_LOG);
    for (uint8_t i = 0; i < USB_HID_KEYBOARD_MAX_KEY_LENTH; i++) {
        osal_printk("0x%02x ", recv_usb_hid_keyboard->key[i]);
    }
    osal_printk("\r\n");
    sle_keyboard_dongle_send_data((usb_hid_keyboard_report_t *)data->data);
}

/**
 * @brief Indication callback for SLE keyboard / SLE键盘指示回调
 * @param[in] client_id Client ID / 客户端ID
 * @param[in] conn_id Connection ID / 连接ID
 * @param[in] data Handle value data / 句柄值数据
 * @param[in] status Operation status / 操作状态
 */
static void sle_keyboard_indication_cb(uint8_t client_id,
                                       uint16_t conn_id,
                                       ssapc_handle_value_t *data,
                                       errcode_t status)
{
    unused(client_id);
    unused(conn_id);
    unused(status);
    usb_hid_keyboard_report_t *recv_usb_hid_keyboard = NULL;
    if (data == NULL || data->data_len == 0 || data->data == NULL) {
        osal_printk("%s sle_keyboard_indication_cb fail, recv data is null!\r\n", SLE_KEYBOARD_DONGLE_LOG);
    }
    osal_printk("%s sle keyboard recive indication\r\n", SLE_KEYBOARD_DONGLE_LOG);
    recv_usb_hid_keyboard = (usb_hid_keyboard_report_t *)data->data;
    osal_printk("%s recv_usb_hid_keyboard.kind = [%d]\r\n", SLE_KEYBOARD_DONGLE_LOG, recv_usb_hid_keyboard->kind);
    osal_printk("%s recv_usb_hid_keyboard.special_key = [%d]\r\n", SLE_KEYBOARD_DONGLE_LOG,
                recv_usb_hid_keyboard->special_key);
    osal_printk("%s recv_usb_hid_keyboard.reversed = [%d]\r\n", SLE_KEYBOARD_DONGLE_LOG,
                recv_usb_hid_keyboard->reversed);
    osal_printk("%s recv_usb_hid_keyboard.key = ", SLE_KEYBOARD_DONGLE_LOG);
    for (uint8_t i = 0; i < USB_HID_KEYBOARD_MAX_KEY_LENTH; i++) {
        osal_printk("0x%02x ", recv_usb_hid_keyboard->key[i]);
    }
    osal_printk("\r\n");
    sle_keyboard_dongle_send_data((usb_hid_keyboard_report_t *)data->data);
}

/**
 * @brief Main task for SLE keyboard dongle / SLE键盘适配器主任务
 * @param[in] arg Task argument / 任务参数
 * @return Task result / 任务结果
 */
static void *sle_keyboard_dongle_task(const char *arg)
{
    unused(arg);
    uint8_t ret;

    osal_printk("%s enter sle_keyboard_dongle_task\r\n", SLE_KEYBOARD_DONGLE_LOG);
    // 1. sle keyboard dongle init
    ret = sle_keyboard_dongle_init();
    if (ret != SLE_KEYBOARD_DONGLE_OK) {
        osal_printk("%s sle_keyboard_dongle_init fail! ret = %d\r\n", SLE_KEYBOARD_DONGLE_LOG, ret);
    }
    // 2. sle keyboard client init
    sle_keyboard_client_init(sle_keyboard_notification_cb, sle_keyboard_indication_cb);
    while (1) {
        osDelay(SLE_KEYBOARD_DONGLE_TASK_DELAY_MS);
    }
    return NULL;
}

/**
 * @brief Entry point for SLE keyboard dongle / SLE键盘适配器入口点
 */
static void sle_keyboard_dongle_entry(void)
{
    osThreadAttr_t attr = {0};

    attr.name = "SLEKeyBoardDongleTask";
    attr.attr_bits = 0U;
    attr.cb_mem = NULL;
    attr.cb_size = 0U;
    attr.stack_mem = NULL;
    attr.stack_size = SLE_KEYBOARD_DONGLE_TASK_STACK_SIZE;
    attr.priority = SLE_KEYBOARD_DONGLE_TASK_PRIO;

    if (osThreadNew((osThreadFunc_t)sle_keyboard_dongle_task, NULL, &attr) == NULL) {
        /* Create task fail. */
    }
}

/** Run the sle_keyboard_entry. / 运行SLE键盘入口 */
app_run(sle_keyboard_dongle_entry);