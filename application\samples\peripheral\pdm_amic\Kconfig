#===============================================================================
# @brief    Kconfig file.
# Copyright (c) @CompanyNameMagicTag 2023-2023. All rights reserved.
#===============================================================================
config ADC_USE_PIN1
    int
    prompt "Choose ADC USE pin1."
    depends on SAMPLE_SUPPORT_PDM_AMIC
    default 30

config ADC_USE_PIN2
    int
    prompt "Choose ADC USE pin2."
    depends on SAMPLE_SUPPORT_PDM_AMIC
    default 31

config ADC_PIN_MODE
    int
    prompt "Choose ADC pin mode."
    default 0
    depends on SAMPLE_SUPPORT_PDM_AMIC

config PDM_TRANSFER_LEN_BY_DMA
    int
    prompt "Set the length of transfer by DMA."
    default 32
    help
        This option means the length of transfer by DMA.

config PDM_MAX_RECORD_DATA_SIZE
    hex
    prompt "Set the max recorder size by PDM."
    default 7800
    help
        This option means the max recorder size by PDM.