# Generated by <PERSON>config Tool.
# Note: !!!This file can not be modify manually!!!

#
# Main menu description, show how to use this configuration system.
#

#
# Targets
#

#
# Select the target.
#
# end of Targets

#
# Application
#

#
# Config the application.
#
CONFIG_USER_FIRMWARE_VERSION="*******"
CONFIG_SYSTEM_CONTROL_ENABLE=y
# CONFIG_POWER_SUPPLY_BY_LDO is not set
# CONFIG_REDUCE_PERP_LS_FREQ is not set
# CONFIG_REDUCE_FREQ_DURING_WFI is not set
# CONFIG_ULTRA_DEEP_SLEEP_ENABLE is not set
# CONFIG_PM_XO_FAST_START_ENABLE is not set
# CONFIG_XO_32K_ENABLE is not set
# CONFIG_SUPPORT_CLOSE_ULP_WDT_DURING_SLP is not set
CONFIG_SAMPLE_ENABLE=y
# CONFIG_ENABLE_BT_SAMPLE is not set
# CONFIG_ENABLE_PERIPHERAL_SAMPLE is not set
CONFIG_ADC_USE_PIN1=30
CONFIG_ADC_USE_PIN2=31
# CONFIG_ENABLE_WIFI_SAMPLE is not set
CONFIG_ENABLE_PRODUCTS_SAMPLE=y
# CONFIG_ENABLE_ALL_PRODUCTS_SAMPLE is not set
# CONFIG_SAMPLE_SUPPORT_AIR_MOUSE_WITH_DONGLE is not set
# CONFIG_SAMPLE_SUPPORT_BLE_KEYBOARD is not set
# CONFIG_SAMPLE_SUPPORT_BLE_MOUSE is not set
# CONFIG_SAMPLE_SUPPORT_BLE_SLE_TAG is not set
# CONFIG_SAMPLE_SUPPORT_BLE_UART is not set
CONFIG_SAMPLE_SUPPORT_RCU=y

#
# rcu Sample Configuration
#
CONFIG_SLE_MTU_LENGTH=300
CONFIG_SLE_MULTICON_NUM=1
CONFIG_SAMPLE_SUPPORT_SLE_RCU_TYPE=y
CONFIG_SAMPLE_SUPPORT_SLE_RCU_SERVER=y
# CONFIG_SAMPLE_SUPPORT_SLE_RCU_DONGLE is not set
# CONFIG_SAMPLE_SUPPORT_BLE_RCU_SERVER is not set
# CONFIG_SAMPLE_SUPPORT_IR is not set
CONFIG_SLE_MULTICON_SERVER_ADDR0=0x0A
CONFIG_SLE_MULTICON_SERVER_ADDR1=0x01
CONFIG_SLE_MULTICON_SERVER_ADDR2=0x02
CONFIG_SLE_MULTICON_SERVER_ADDR3=0x03
CONFIG_SLE_MULTICON_SERVER_ADDR4=0x04
CONFIG_SLE_MULTICON_SERVER_ADDR5=0x05
CONFIG_SLE_MULTICON_SERVER_NAME="sle_rcu_server1"
CONFIG_USB_UAC_MAX_RECORD_SIZE=0x400
CONFIG_USB_PDM_TRANSFER_LEN_BY_DMA=256
# CONFIG_RCU_MASS_PRODUCTION_TEST is not set
# CONFIG_APP_LOG_ENABLE is not set
# end of rcu Sample Configuration

# CONFIG_SAMPLE_SUPPORT_SLE_OTA_DONGLE is not set
# CONFIG_SAMPLE_SUPPORT_SLE_MULTI_CONN is not set
# CONFIG_SAMPLE_SUPPORT_SLE_MICROPHONE is not set
# CONFIG_SAMPLE_SUPPORT_SLE_MOUSE_WITH_DONGLE is not set
CONFIG_MOUSE_ADC_VBAT_CH=7
# CONFIG_SAMPLE_SUPPORT_SLE_UART is not set
# CONFIG_SAMPLE_SUPPORT_USB_AMIC_VDT is not set
# CONFIG_SAMPLE_SUPPORT_USB_KEYBOARD is not set
# CONFIG_SAMPLE_SUPPORT_USB_MOUSE is not set
# CONFIG_SAMPLE_SUPPORT_SLE_MEASURE_DIS is not set
# CONFIG_SAMPLE_SUPPORT_SLEKEY_NFC is not set
# CONFIG_SAMPLE_SUPPORT_LOWPOWER is not set
# CONFIG_ENABLE_NFC_SAMPLE is not set
CONFIG_SYSTEM_MOUSE_PIN_CONFIG=y

#
# System mouse Pin Configuration
#
CONFIG_MOUSE_PIN_LEFT=0
CONFIG_MOUSE_PIN_MID=1
CONFIG_MOUSE_PIN_RIGHT=2
CONFIG_MOUSE_PIN_QDEC_A=3
CONFIG_MOUSE_PIN_QDEC_B=4
CONFIG_MOUSE_PIN_QDEC_COMMON=5
CONFIG_MOUSE_PIN_NRESET=21
CONFIG_MOUSE_PIN_MONTION=6
CONFIG_MOUSE_PIN_SPI_CLK=18
CONFIG_MOUSE_PIN_SPI_CS=19
CONFIG_MOUSE_PIN_SPI_MOSI=17
CONFIG_MOUSE_PIN_SPI_MISO=16
# end of System mouse Pin Configuration
# end of Application

#
# Bootloader
#

#
# Config the bootloader.
#
# CONFIG_BOOT_SUPPORT_SEC is not set
# CONFIG_BOOT_SUPPORT_SECURE_VERIFY is not set
# CONFIG_BOOT_SUPPORT_PARTITION is not set
# CONFIG_LOADERBOOT_SUPPORT_EFUSE_BURN is not set
# CONFIG_LOADERBOOT_SUPPORT_FLASH_CHIP_ERASE is not set
# CONFIG_LOADERBOOT_SUPPORT_UPLOAD_DATA is not set
# CONFIG_LOADERBOOT_SUPPORT_READ_VERSION is not set
# CONFIG_LOADERBOOT_SUPPORT_SET_BUADRATE is not set
# CONFIG_LOADERBOOT_SUPPORT_CUSTOMIZED_READ_EFUSE is not set
# end of Bootloader

#
# Drivers
#

#
# Config the drivers.
#

#
# Boards
#

#
# Config the drivers of boards.
#
# end of Boards

#
# Chips
#

#
# Config the drivers of chips.
#
# CONFIG_CHIP_WS53 is not set
# CONFIG_CHIP_WS63 is not set
CONFIG_CHIP_BS2X=y

#
# Chip Configurations for bs2x
#

#
# bs2x chipset resource config
#
CONFIG_SPI_BUS_MAX_NUM=1
CONFIG_SUPPORT_LOG_THREAD=y
# end of Chip Configurations for bs2x
# end of Chips

#
# Drivers
#

#
# Config the drivers.
#
CONFIG_DRIVER_SUPPORT_ADC=y

#
# ADC Configuration
#

#
# Config ADC
#
# CONFIG_ADC_SUPPORT_AUTO_SCAN is not set
# CONFIG_ADC_SUPPORT_QUERY_REGS is not set
# CONFIG_ADC_USING_V151 is not set
# CONFIG_ADC_USING_V152 is not set
CONFIG_ADC_USING_V153=y
# CONFIG_ADC_USING_V154 is not set
# CONFIG_ADC_USING_V155 is not set
CONFIG_ADC_SUPPORT_AFE=y
CONFIG_ADC_SUPPORT_DIFFERENTIAL=y
CONFIG_ADC_SUPPORT_AMIC=y
# end of ADC Configuration

# CONFIG_DRIVER_SUPPORT_CAN_FD is not set
CONFIG_DRIVER_SUPPORT_CPUTRACE=y

#
# CPUTRACE Configuration
#

#
# Config  CPUTRACE
#
CONFIG_CPU_TRACE_SUPPORT_LPM=y
# CONFIG_CPUTRACE_EXIST_BTOP_GLUE is not set
# end of CPUTRACE Configuration

CONFIG_DRIVER_SUPPORT_DMA=y

#
# DMA Configuration
#

#
# Config DMA
#
CONFIG_DMA_SUPPORT_LLI=y
CONFIG_DMA_SUPPORT_CIRCULAR_LLI=y
CONFIG_DMA_SUPPORT_LPM=y
CONFIG_DMA_SUPPORT_CLOCK=y
# CONFIG_DMA_SUPPORT_QUERY_REGS is not set
CONFIG_DMA_USING_V100=y
CONFIG_DMA_USING_V120=y
# CONFIG_DMA_USING_V151 is not set
# CONFIG_DMA_SUPPORT_SMDMA is not set
# CONFIG_SUPPORT_DATA_CACHE is not set
# CONFIG_DMA_LLI_NODE_FIX_MEM is not set
# end of DMA Configuration

# CONFIG_DRIVER_SUPPORT_EDGE is not set
# CONFIG_DRIVER_SUPPORT_EFLASH is not set
CONFIG_DRIVER_SUPPORT_EFUSE=y

#
# EFUSE Configuration
#

#
# Config EFUSE
#
CONFIG_EFUSE_USING_V151=y
CONFIG_EFUSE_SWITCH_EN=y
CONFIG_EFUSE_REGION_NUM=1
# end of EFUSE Configuration

CONFIG_DRIVER_SUPPORT_FLASH=y

#
# FLASH Configuration
#

#
# Config FLASH
#
# CONFIG_FLASH_SUPPORT_XIP is not set
CONFIG_SPI_WAIT_FIFO_LONG_TIMEOUT=0xFFFFFFFF
CONFIG_SPI_TRAN_MAX_TIMEOUT=0xFFFFFFFF
CONFIG_SPI_WAIT_READ_TIMEOUT=0xFFFFFFFF
CONFIG_FLASH_POWER_ON_TIMEOUT=0xFF
CONFIG_SPI_RX_FIFO_THRESHOLD=0x8
CONFIG_SPI_TX_FIFO_THRESHOLD=0x8
# CONFIG_FLASH_ALREADY_START is not set
# CONFIG_FLASH_SUPPORT_LPC is not set
CONFIG_FLASH_SUPPORT_SPI_SINGLE_LINE_MODE=y
# CONFIG_FLASH_USE_SPI_SINGLE_LINE_MODE_ONLY is not set
# CONFIG_FLASH_USE_CUSTOMIZED_DEVICE_INFO is not set
# end of FLASH Configuration

CONFIG_DRIVER_SUPPORT_GPIO=y

#
# GPIO Configuration
#

#
# Config GPIO
#
CONFIG_GPIO_SUPPORT_LPM=y
# CONFIG_GPIO_SELECT_CORE is not set
# CONFIG_GPIO_USING_V100 is not set
CONFIG_GPIO_USING_V150=y
CONFIG_GPIO_BANK_NUM=1
CONFIG_GPIO0_WIDTH=32
# CONFIG_GPIO_SUPPORT_MULTISYSTEM is not set
# end of GPIO Configuration

# CONFIG_DRIVER_SUPPORT_HASH is not set
# CONFIG_DRIVER_SUPPORT_I2C is not set
# CONFIG_DRIVER_SUPPORT_I2S is not set
# CONFIG_DRIVER_SUPPORT_IPC is not set
CONFIG_DRIVER_SUPPORT_IR=y

#
# IR Configuration
#

#
# Config IR
#
CONFIG_SAMPLE_SUPPORT_IR_STUDY=y
CONFIG_SAMPLE_IR_SEND_PIN=29
CONFIG_SAMPLE_IR_STUDY_PIN=28
CONFIG_IR_STUDY_RX_IN=4
CONFIG_IR_STUDY_RX_OUT=5
CONFIG_IR_STUDY_MIC_N=6
# end of IR Configuration

CONFIG_DRIVER_SUPPORT_KEYSCAN=y

#
# KEYSCAN Configuration
#

#
# Config  KEYSCAN
#
CONFIG_KEYSCAN_REPORT_MAX_NUMS=6
CONFIG_KEYSCAN_SUPPORT_LPM=y
# CONFIG_KEYSCAN_SUPPORT_SLEEP is not set
CONFIG_KEYSCAN_IDLE_WAIT_US=********
# CONFIG_KEYSCAN_SUPPORT_SW_DEFENCE is not set
# CONFIG_KEYSCAN_USE_FULL_KEYS_TYPE is not set
CONFIG_KEYSCAN_USE_SIX_KEYS_TYPE=y
# CONFIG_KEYSCAN_USER_CONFIG_TYPE is not set
CONFIG_KEYSCAN_ENABLE_COL=2
CONFIG_KEYSCAN_ENABLE_ROW=3
# CONFIG_KEYSCAN_ENABLE_REP_ALL_KEY is not set
# end of KEYSCAN Configuration

# CONFIG_DRIVER_SUPPORT_MEM_MONITOR is not set
# CONFIG_DRIVER_SUPPORT_MPU is not set
# CONFIG_DRIVER_SUPPORT_PCM is not set
CONFIG_DRIVER_SUPPORT_PDM=y

#
# PDM Configuration
#

#
# Config PDM
#
CONFIG_PDM_SUPPORT_LPC=y
# CONFIG_PDM_SUPPORT_QUERY_REGS is not set
CONFIG_PDM_USING_V150=y
# CONFIG_PDM_USING_V151 is not set
CONFIG_MIC_CH_NUM=2
CONFIG_PDM_AFIFO_AEMPTY_TH=3
CONFIG_PDM_AFIFO_AFULL_TH=16
CONFIG_PDM_ADC_DC_OFFSET=0
CONFIG_PDM_CIC_GAIN=0x14
CONFIG_PDM_LP_CIC_GAIN=0x0
# end of PDM Configuration

CONFIG_DRIVER_SUPPORT_PINCTRL=y

#
# PINCTRL Configuration
#

#
# Config  PINCTRL
#
CONFIG_PINCTRL_SUPPORT_LPM=y
# CONFIG_PINCTRL_USING_V150 is not set
CONFIG_PINCTRL_USING_BS2X=y
# CONFIG_PINCTRL_USING_CHIPS is not set
CONFIG_PINCTRL_SUPPORT_IE=y
# CONFIG_PINCTRL_SUPPORT_ST is not set
# end of PINCTRL Configuration

CONFIG_DRIVER_SUPPORT_PM=y

#
# PM Configuration
#

#
# Config pm
#
CONFIG_PM_SUPPORT_CLOCK_CRG_API=y
# end of PM Configuration

CONFIG_DRIVER_SUPPORT_PMP=y

#
# PMP Configuration
#

#
# Config pmp
#
CONFIG_PMP_USING_RISCV_31=y
# CONFIG_PMP_USING_RISCV_70 is not set
# end of PMP Configuration

CONFIG_DRIVER_SUPPORT_PWM=y

#
# PWM Configuration
#

#
# Config PWM
#
CONFIG_PWM_USING_V151=y
# CONFIG_PWM_USING_V150 is not set
CONFIG_PWM_GROUP_NUM=6
CONFIG_PWM_CHANNEL_NUM=12
# CONFIG_PWM_PRELOAD is not set
# end of PWM Configuration

CONFIG_DRIVER_SUPPORT_RTC=y

#
# RTC Configuration
#

#
# Config RTC
#
CONFIG_RTC_MAX_RTCS_NUM=16
CONFIG_RTC_CLOCK_VALUE=32768
# CONFIG_RTC_USING_OLD_VERSION is not set
# CONFIG_RTC_SUPPORT_LPM is not set
# CONFIG_RTC_USING_V100 is not set
CONFIG_RTC_USING_V150=y
CONFIG_RTC_MAX_NUM=4
CONFIG_RTC_0_WIDTH_64=1
CONFIG_RTC_1_WIDTH_64=1
CONFIG_RTC_2_WIDTH_64=1
CONFIG_RTC_3_WIDTH_64=1
CONFIG_RTC_STOP_DELAY_SUPPORT=y
# CONFIG_RTC_STOP_DELAY_USING_OSAL is not set
# CONFIG_RTC_STOP_DELAY_USING_TCXO is not set
CONFIG_RTC_STOP_DELAY_USING_SYSTICK=y
CONFIG_RTC_START_DELAY_SUPPORT=y
# CONFIG_RTC_START_DELAY_USING_OSAL is not set
# CONFIG_RTC_START_DELAY_USING_TCXO is not set
CONFIG_RTC_START_DELAY_USING_SYSTICK=y
# end of RTC Configuration

# CONFIG_DRIVER_SUPPORT_SDIO is not set
# CONFIG_DRIVER_SUPPORT_SEC is not set
CONFIG_DRIVER_SUPPORT_SECURITY_UNIFIED=y

#
# SECURITY_UNIFIED Configuration
#

#
# Config security_unified
#
# CONFIG_SECURITY_UNIFIED_SUPPORT_DEEP_SLEEP is not set
# CONFIG_SECURITY_UNIFIED_SUPPORT_SYMC is not set
# CONFIG_SECURITY_UNIFIED_SUPPORT_HASH is not set
CONFIG_SECURITY_UNIFIED_SUPPORT_TRNG=y
# CONFIG_SECURITY_UNIFIED_SUPPORT_KM is not set
# CONFIG_SECURITY_UNIFIED_SUPPORT_FAPC is not set
# CONFIG_SECURITY_UNIFIED_SUPPORT_PKE is not set
CONFIG_ENTROPY_SOURCES_FROM_FRO=y
CONFIG_TRNG_RING_ENABLE=y
# CONFIG_SM_EFUSE_CHECK is not set
# end of SECURITY_UNIFIED Configuration

CONFIG_DRIVER_SUPPORT_SFC=y

#
# SFC Configuration
#

#
# Config  SFC
#
# CONFIG_SFC_SUPPORT_DMA is not set
# CONFIG_SFC_ALLOW_ERASE_WRITEBACK is not set
CONFIG_SFC_ALREADY_INIT=y
CONFIG_SFC_SUPPORT_LPM=y
# CONFIG_SFC_SUPPORT_DATA_CACHE is not set
# CONFIG_SFC_SUPPORT_RWE_INDEPENDENT is not set
CONFIG_SFC_SUPPORT_WRITE_PROTECT=y
CONFIG_SFC_USE_CUSTOMIZED_DEVICE_INFO=y
# CONFIG_SFC_DEBUG is not set
# end of SFC Configuration

CONFIG_DRIVER_SUPPORT_SPI=y

#
# SPI Configuration
#

#
# Config SPI
#
CONFIG_SPI_SUPPORT_MASTER=y
# CONFIG_SPI_SUPPORT_SLAVE is not set
# CONFIG_SPI_SUPPORT_DMA is not set
# CONFIG_SPI_SUPPORT_INTERRUPT is not set
# CONFIG_SPI_SUPPORT_CONCURRENCY is not set
# CONFIG_SPI_SUPPORT_LOOPBACK is not set
# CONFIG_SPI_SUPPORT_CRC is not set
CONFIG_SPI_MAX_TIMEOUT=0xFFFFFFFF
CONFIG_SPI_TXFTLR=16
CONFIG_SPI_RXFTLR=16
CONFIG_SPI_SUPPORT_LPC=y
# CONFIG_SPI_SUPPORT_LPM is not set
CONFIG_SPI_SUPPORT_SINGLE_SPI=y
CONFIG_SPI_NOT_SUPPORT_TEXAS_FORMAT=y
# CONFIG_SPI_SUPPORT_QUAD_SPI is not set
# CONFIG_SPI_SUPPORT_QUERY_REGS is not set
# CONFIG_SPI_SUPPORT_TXRX_TRANS_MODE is not set
# CONFIG_SPI_USING_V100 is not set
CONFIG_SPI_USING_V151=y
# end of SPI Configuration

# CONFIG_DRIVER_SUPPORT_SSI is not set
CONFIG_DRIVER_SUPPORT_SYSTICK=y

#
# SYSTICK Configuration
#

#
# Config SYSTICK
#
CONFIG_SYSTICK_SUPPORT_LPM=y
# CONFIG_SYSTICK_WITH_TWO_DATA_REGS is not set
# end of SYSTICK Configuration

CONFIG_DRIVER_SUPPORT_TCXO=y

#
# TCXO Configuration
#

#
# Config TCXO
#
CONFIG_TCXO_SUPPORT_LPM=y
# CONFIG_TCXO_WITH_TWO_DATA_REGS is not set
# end of TCXO Configuration

CONFIG_DRIVER_SUPPORT_TIMER=y

#
# TIMER Configuration
#

#
# Config TIMER
#
CONFIG_TIMER_MAX_TIMERS_NUM=8
CONFIG_TIMER_CLOCK_VALUE=32000000
# CONFIG_TIMER_SUPPORT_LPC is not set
# CONFIG_TIMER_USING_OLD_VERSION is not set
CONFIG_TIMER_SUPPORT_LPM=y
CONFIG_TIMER_SUPPORT_HIGH_PRECISION=y
# CONFIG_TIMER_USING_V100 is not set
CONFIG_TIMER_USING_V150=y
CONFIG_TIMER_MAX_NUM=1
CONFIG_TIMER_0_WIDTH_64=0
# end of TIMER Configuration

# CONFIG_DRIVER_SUPPORT_TSENSOR is not set
CONFIG_DRIVER_SUPPORT_UART=y

#
# Uart Configuration
#

#
# Config uart
#
CONFIG_UART_BAUD_RATE_DIV_8=y
CONFIG_UART_SUPPORT_TX=y
CONFIG_UART_SUPPORT_TX_INT=y
CONFIG_UART_SUPPORT_RX=y
# CONFIG_UART_SUPPORT_DMA is not set
# CONFIG_UART_SUPPORT_SEND_IN_DMA_ISR is not set
# CONFIG_UART_SUPPORT_FLOW_CTRL is not set
CONFIG_UART_SUPPORT_LPM=y
CONFIG_UART_FIFO_DEPTH=64
CONFIG_UART_SUPPORT_LPC=y
# CONFIG_UART_MULTI_CORE_RESUME is not set
# CONFIG_UART_NOT_SUPPORT_RX_CONDITON_SIZE_OPTIMIZE is not set
# CONFIG_UART_SUPPORT_QUERY_REGS is not set
CONFIG_UART_L0_TX_PIN=19
CONFIG_UART_L0_RX_PIN=20
CONFIG_UART_H0_TX_PIN=17
CONFIG_UART_H0_RX_PIN=18
CONFIG_UART_DLF_SIZE=4
# CONFIG_UART_USING_V120 is not set
CONFIG_UART_USING_V151=y
# CONFIG_UART_USING_V100 is not set
# CONFIG_UART_IP_VERSION_V151_PRO is not set
# CONFIG_UART_USING_LP_UART is not set
# end of Uart Configuration

CONFIG_DRIVER_SUPPORT_USB=y

#
# USB Configuration
#

#
# Config USB
#
CONFIG_DRIVERS_USB=y
CONFIG_DRIVERS_USB_DRIVER=y

#
# USB Controller Config
#
CONFIG_DRIVERS_USB2_DEVICE_CONTROLLER=y
# CONFIG_DRIVERS_USB2_PORT_DISCONNECT is not set
# CONFIG_DRIVERS_USB2_ULPI_INTERFANCE is not set
CONFIG_DRIVERS_USB2_OTG_SUPPORT_ISO_TRANSFER=y
CONFIG_DRIVERS_USB2_OTG_DFIFO_DYNAMIC=y

#
# USB Endpoints Max Packet Size
#
CONFIG_DRIVERS_USB2_OUT_EP_MPS=512
CONFIG_DRIVERS_USB2_IN_EP_INDEX1_MPS=512
CONFIG_DRIVERS_USB2_IN_EP_INDEX2_MPS=512
CONFIG_DRIVERS_USB2_IN_EP_INDEX3_MPS=128
# end of USB Endpoints Max Packet Size

# CONFIG_DRIVERS_USB_DEVICE_CUSTOM_CTRLREQ_BUF_SIZE is not set
# CONFIG_DRIVERS_USB3_DEVICE_CONTROLLER is not set
CONFIG_DRIVERS_USB_DEVICE_SLAVE_CONFIG=y
CONFIG_USB_OUT_EP_NUM=3
CONFIG_USB_IN_EP_NUM=3
CONFIG_USB_DMA_OFFSET=0x0
# end of USB Controller Config

CONFIG_DRIVERS_USB_GADGET=y

#
# USB Gadget Drivers
#
# CONFIG_DRIVERS_USB_UVC_GADGET is not set
CONFIG_DRIVERS_USB_UAC_GADGET=y
CONFIG_DRIVERS_USB_UAC_GADGET_VER_1_0=y
CONFIG_DRIVERS_USB_UAC_GADGET_VER_1_0_SPEAKER=y
# CONFIG_DRIVERS_USB_UAC_GADGET_VER_2_0 is not set
# CONFIG_DRIVERS_USB_DFU_GADGET is not set
# CONFIG_DRIVERS_USB_SERIAL_GADGET is not set
CONFIG_DRIVERS_USB_HID_GADGET=y
CONFIG_DRIVERS_USB_HID_FUNC_INTERFACE=y
CONFIG_DRIVERS_USB_HID_REPORT_MAP_NUM=3
CONFIG_DRIVERS_USB_HID_POLLING_REPORTS=y
# CONFIG_DRIVERS_USB_HID_CUSTOM is not set
CONFIG_DRIVERS_USB_HID_INPUT_REPORT_LEN=12
CONFIG_DRIVERS_USB_HID_OUTPUT_REPORT=y
CONFIG_DRIVERS_USB_HID_OUTPUT_REPORT_EVENT=y
# CONFIG_DRIVERS_USB_HID_OUTPUT_REPORT_CALLBACK is not set
CONFIG_DRIVERS_USB_UAC_HID_GADGET=y
# CONFIG_DRIVERS_USB_ACM_HID_GADGET is not set
# end of USB Gadget Drivers

CONFIG_DRIVERS_USB_COMPOSITE_GADGET=y
CONFIG_BASE_CORE_TICK_PER_SECOND=1000
# end of USB Configuration

CONFIG_DRIVER_SUPPORT_WDT=y

#
# Watchdog Configuration
#

#
# Config watchdog
#
# CONFIG_WATCHDOG_ALREADY_START is not set
CONFIG_WATCHDOG_SUPPORT_ULP_WDT=y
CONFIG_WATCHDOG_SUPPORT_LPM=y
CONFIG_WATCHDOG_USING_V151=y
# CONFIG_WATCHDOG_USING_V100 is not set
# CONFIG_WATCHDOG_USING_V152 is not set
CONFIG_WATCHDOG_USING_V151_RST_PL=7
# end of Watchdog Configuration

# CONFIG_DRIVER_SUPPORT_AUDIO is not set
# end of Drivers
# end of Drivers

#
# Kernel
#

#
# Config the kernel.
#
CONFIG_DRIVER_SUPPORT_LITEOS=y
# CONFIG_DRIVER_SUPPORT_LITEOS_208_6_0_B017_CAT1 is not set
CONFIG_DRIVER_SUPPORT_LITEOS_208_6_0_B017=y
# CONFIG_DRIVER_SUPPORT_LITEOS_208_5_0_B004 is not set
# CONFIG_DRIVER_SUPPORT_LITEOS_208_5_0 is not set
# CONFIG_DRIVER_SUPPORT_LITEOS_207_0_0 is not set

#
# osal
#

#
# Config osal.
#
# CONFIG_DRIVER_DISABLE_OSAL_LOG is not set
# end of osal
# end of Kernel

#
# Middleware
#

#
# Config the middleware.
#

#
# Chips
#

#
# Config the middleware of chips.
#
CONFIG_MIDDLEWARE_CHIP_WS53=y
# CONFIG_MIDDLEWARE_CHIP_WS63 is not set

#
# Chip Configurations for ws53
#

#
# Config ws53
#
# CONFIG_MIDDLEWARE_SUPPORT_FTM is not set
# CONFIG_MIDDLEWARE_SUPPORT_NV is not set
# CONFIG_MIDDLEWARE_SUPPORT_UPG is not set
# end of Chip Configurations for ws53
# end of Chips

#
# Services
#

#
# Config the middleware of service.
#
# CONFIG_MIDDLEWARE_SUPPORT_TIOT is not set
# end of Services

#
# Utils
#

#
# Config the middleware of utils.
#
CONFIG_MIDDLEWARE_SUPPORT_PM=y

#
# PM Configuration
#

#
# Config PM
#
# CONFIG_PM_SLEEP_RECORD_ENABLE is not set
CONFIG_PM_POWER_GATING_ENABLE=y
# CONFIG_PM_VETO_TRACK_ENABLE is not set
CONFIG_PM_LIGHT_SLEEP_THRESHOLD_MS=5
CONFIG_PM_DEEP_SLEEP_THRESHOLD_MS=10
CONFIG_PM_DEBUG=y
CONFIG_PM_FSM_TRACE_NUM=32
CONFIG_PM_ENABLE_WAKEUP_INTERRUPT=y
CONFIG_PM_SYS_SUPPORT=y
CONFIG_PM_SYS_SUPPORT_MSGQUEUE=y
CONFIG_PM_SYS_STACK_SIZE=2048
# end of PM Configuration

CONFIG_MIDDLEWARE_SUPPORT_DFX=y

#
# Design for Maintainable and Testable Configuration
#

#
# Config DFM & DFT
#
# CONFIG_DFX_SUPPORT_USERS_PRINT is not set
# CONFIG_DFX_SUPPORT_PRINT is not set
# CONFIG_CONFIG_ERRCODE_SUPPORT_REPORT is not set
# end of Design for Maintainable and Testable Configuration

CONFIG_MIDDLEWARE_SUPPORT_AT=y

#
# Config AT
#

#
# Config AT
#
# CONFIG_AT_SUPPORT_PLT is not set
# end of Config AT

CONFIG_MIDDLEWARE_SUPPORT_CODEC=y

#
# Config Codec
#

#
# Config Codec
#
CONFIG_CODEC_ENABLE_SBC=y
# CONFIG_CODEC_ENABLE_MSBC is not set
# CONFIG_CODEC_ENABLE_OPUS is not set
# CONFIG_CODEC_ENABLE_L2HC is not set
# end of Config Codec

# CONFIG_MIDDLEWARE_SUPPORT_UPDATE is not set
# end of Utils
# end of Middleware

#
# Protocol
#

#
# Config the protocol.
#

#
# bt_host
#

#
# Config bluetooth.
#
CONFIG_AT_BLE=y

#
# AT_BLE
#

#
# Config bluetooth host AT BLE.
#
CONFIG_AT_BLE_LEVEL_ONE=y
CONFIG_AT_BLE_LEVEL_TWO=y
CONFIG_AT_BLE_MODULE_GAP_COMMON=y
CONFIG_AT_BLE_MODULE_GAP_SLAVE=y
CONFIG_AT_BLE_MODULE_GAP_MASTER=y
CONFIG_AT_BLE_MODULE_GATT_CLIENT=y
CONFIG_AT_BLE_MODULE_GATT_SERVER=y
CONFIG_AT_BLE_SERVICE_HID_MOUSE=y
CONFIG_AT_BLE_SERVICE_HID_KEYBOARD=y
CONFIG_AT_BLE_SERVICE_UUID_SERVER=y
CONFIG_AT_BLE_SERVICE_UUID_CLIENT=y
CONFIG_AT_BLE_REGISTER_TEST=y
# CONFIG_AT_PTS_TEST_SUPPORT is not set
# end of AT_BLE

CONFIG_AT_GLE=y

#
# AT_SLE
#

#
# Config bluetooth host AT SLE.
#
CONFIG_AT_GLE_LEVEL_ONE=y
CONFIG_AT_GLE_LEVEL_TWO=y
CONFIG_AT_GLE_MODULE_SSAPC=y
CONFIG_AT_GLE_MODULE_SSAPS=y
CONFIG_AT_GLE_MODULE_DD=y
CONFIG_AT_GLE_MODULE_CM=y
CONFIG_AT_GLE_MODULE_SAMPLE=y
CONFIG_AT_GLE_MODULE_LOW_LATENCY=y
# CONFIG_AT_GLE_MODULE_DFX_CONFIG is not set
CONFIG_AT_GLE_MODULE_TM_SIGNAL=y
# end of AT_SLE

#
# FEATURE_GLE
#

#
# Config bluetooth host SLE feature.
#
# CONFIG_FEATURE_GLE_LOW_LATENCY is not set
# CONFIG_FEATURE_GLE_HADM is not set
# CONFIG_FEATURE_GLE_PERFORMANCE_SUPPORT is not set
# end of FEATURE_GLE
# end of bt_host

#
# bt_controller
#

#
# Config bluetooth.
#
# end of bt_controller

#
# nfc
#
# CONFIG_SUPPORT_NFC_SERVICE is not set
# end of nfc

#
# wifi
#

#
# Config wifi.
#
# end of wifi

# CONFIG_RADAR_SERVICE is not set
# end of Protocol

#
# Test
#

#
# Config the test.
#
CONFIG_TEST_SUPPORT_TESTSUITE=y

#
# Testsuite Configuration
#

#
# Config Testsuite
#
CONFIG_TEST_SUITE_FUNCTIONS_MAX_FUNCS=300
CONFIG_TEST_CONSOLE_HISTORY_LEN=2
CONFIG_TEST_SUITE_TASK_STACK_SIZE=0x800
# CONFIG_TEST_SUPPORT_SECURITY_UNIFIED is not set
# end of Testsuite Configuration
# end of Test
