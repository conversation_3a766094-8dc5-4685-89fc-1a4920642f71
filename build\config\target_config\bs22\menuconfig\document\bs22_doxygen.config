# Generated by Kconfig Tool.
# Note: !!!This file can not be modify manually!!!

#
# Main menu description, show how to use this configuration system.
#

#
# Targets
#

#
# Select the target.
#
# end of Targets

#
# Application
#

#
# Config the application.
#
# end of Application

#
# Bootloader
#

#
# Config the bootloader.
#
# end of Bootloader

#
# Drivers
#

#
# Config the drivers.
#

#
# Boards
#

#
# Config the drivers of boards.
#
# end of Boards

#
# Chips
#

#
# Config the drivers of chips.
#
# end of Chips

#
# Drivers
#

#
# Config the drivers.
#
# CONFIG_DRIVER_SUPPORT_WDT is not set
# CONFIG_DRIVER_SUPPORT_PDM is not set
# CONFIG_DRIVER_SUPPORT_EFUSE is not set
# end of Drivers
# end of Drivers

#
# Kernel
#

#
# Config the kernel.
#
CONFIG_DRIVER_SUPPORT_LITEOS=y
CONFIG_DRIVER_SUPPORT_LITEOS_207_0_0=y
# end of Kernel

#
# Middleware
#

#
# Config the middleware.
#

#
# Chips
#

#
# Config the middleware of chips.
#
# end of Chips

#
# Services
#

#
# Config the middleware of service.
#
# end of Services

#
# Utils
#

#
# Config the middleware of utils.
#
CONFIG_TEST_SUPPORT_TESTSUITE=y

#
# Testsuite Configuration
#

#
# Config Testsuite
#
CONFIG_TEST_SUITE_FUNCTIONS_MAX_FUNCS=200
CONFIG_TEST_CONSOLE_HISTORY_LEN=10
# end of Testsuite Configuration
# end of Utils
# end of Middleware

#
# Protocol
#

#
# Config the protocol.
#
# end of Protocol
